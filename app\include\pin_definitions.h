#ifndef __PIN_DEFINITIONS_H__
#define __PIN_DEFINITIONS_H__

#include "system.h"

/**
 * @file pin_definitions.h
 * @brief STM32L072CBT6 MCU引脚分配与功能定义
 * @note 根据doc/requirements.md中的引脚分配要求定义
 */

/* ========================================================================== */
/* Cat1模块接口（EC600S）引脚定义                                              */
/* ========================================================================== */
#define CAT1_UART_TX_PORT           GPIOA
#define CAT1_UART_TX_PIN            GPIO_PIN_10
#define CAT1_UART_RX_PORT           GPIOA  
#define CAT1_UART_RX_PIN            GPIO_PIN_9

#define CAT1_PWR_EN_PORT            GPIOB
#define CAT1_PWR_EN_PIN             GPIO_PIN_0
#define CAT1_PWR_KEY_PORT           GPIOB
#define CAT1_PWR_KEY_PIN            GPIO_PIN_1
#define CAT1_STATUS_PORT            GPIOB
#define CAT1_STATUS_PIN             GPIO_PIN_2
#define CAT1_RST_N_PORT             GPIOB
#define CAT1_RST_N_PIN              GPIO_PIN_9

/* Cat1模块控制宏定义 */
#define CAT1_POWER(x)               HAL_GPIO_WritePin(CAT1_PWR_EN_PORT, CAT1_PWR_EN_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define CAT1_PWR_KEY(x)             HAL_GPIO_WritePin(CAT1_PWR_KEY_PORT, CAT1_PWR_KEY_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define CAT1_RST(x)                 HAL_GPIO_WritePin(CAT1_RST_N_PORT, CAT1_RST_N_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define CAT1_STATUS_READ()          HAL_GPIO_ReadPin(CAT1_STATUS_PORT, CAT1_STATUS_PIN)

/* ========================================================================== */
/* LCD显示接口（SPI控制ST7567）引脚定义                                        */
/* ========================================================================== */
#define LCD_MOSI_PORT               GPIOB
#define LCD_MOSI_PIN                GPIO_PIN_3
#define LCD_SCK_PORT                GPIOB
#define LCD_SCK_PIN                 GPIO_PIN_4
#define LCD_A0_PORT                 GPIOB
#define LCD_A0_PIN                  GPIO_PIN_5
#define LCD_RST_PORT                GPIOB
#define LCD_RST_PIN                 GPIO_PIN_6
#define LCD_CS_PORT                 GPIOB
#define LCD_CS_PIN                  GPIO_PIN_7
#define LCD_PWR_EN_PORT             GPIOA
#define LCD_PWR_EN_PIN              GPIO_PIN_12

/* LCD控制宏定义 */
#define LCD_CS(x)                   HAL_GPIO_WritePin(LCD_CS_PORT, LCD_CS_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define LCD_RST(x)                  HAL_GPIO_WritePin(LCD_RST_PORT, LCD_RST_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define LCD_A0(x)                   HAL_GPIO_WritePin(LCD_A0_PORT, LCD_A0_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define LCD_CLK(x)                  HAL_GPIO_WritePin(LCD_SCK_PORT, LCD_SCK_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define LCD_SDA(x)                  HAL_GPIO_WritePin(LCD_MOSI_PORT, LCD_MOSI_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define LCD_PWR_EN(x)               HAL_GPIO_WritePin(LCD_PWR_EN_PORT, LCD_PWR_EN_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)

/* ========================================================================== */
/* RS485通信接口（UART2）引脚定义                                             */
/* ========================================================================== */
#define RS485_TX_PORT               GPIOA
#define RS485_TX_PIN                GPIO_PIN_2
#define RS485_RX_PORT               GPIOA
#define RS485_RX_PIN                GPIO_PIN_3
#define RS485_TX_EN_PORT            GPIOB
#define RS485_TX_EN_PIN             GPIO_PIN_8
#define RS485_PWR_PORT              GPIOA
#define RS485_PWR_PIN               GPIO_PIN_11

/* RS485控制宏定义 */
#define RS485_TX_EN(x)              HAL_GPIO_WritePin(RS485_TX_EN_PORT, RS485_TX_EN_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define RS485_PWR(x)                HAL_GPIO_WritePin(RS485_PWR_PORT, RS485_PWR_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)

/* ========================================================================== */
/* 按键与唤醒接口引脚定义                                                      */
/* ========================================================================== */
#define WAKE_KEY_PORT               GPIOC
#define WAKE_KEY_PIN                GPIO_PIN_13

/* 按键读取宏定义 */
#define WAKE_KEY_READ()             HAL_GPIO_ReadPin(WAKE_KEY_PORT, WAKE_KEY_PIN)

/* ========================================================================== */
/* 电池监测与调试串口引脚定义                                                  */
/* ========================================================================== */
#define BAT_ADC_PORT                GPIOA
#define BAT_ADC_PIN                 GPIO_PIN_4
#define DEBUG_UART_TX_PORT          GPIOA
#define DEBUG_UART_TX_PIN           GPIO_PIN_0
#define DEBUG_UART_RX_PORT          GPIOA
#define DEBUG_UART_RX_PIN           GPIO_PIN_1

/* ========================================================================== */
/* 系统状态指示灯引脚定义                                                      */
/* ========================================================================== */
#define SYS_LED_PORT                GPIOB
#define SYS_LED_PIN                 GPIO_PIN_12

/* 系统LED控制宏定义 */
#define SYS_LED(x)                  HAL_GPIO_WritePin(SYS_LED_PORT, SYS_LED_PIN, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define SYS_LED_TOGGLE()            HAL_GPIO_TogglePin(SYS_LED_PORT, SYS_LED_PIN)

/* ========================================================================== */
/* 注意：引脚初始化分别在各个模块中实现                                          */
/* ========================================================================== */

#endif /* __PIN_DEFINITIONS_H__ */
