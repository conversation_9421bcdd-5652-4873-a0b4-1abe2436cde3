/**
 * stm32l072cbt6 32MHz 128kb flash + 16kb ram + 6K eeprom
 */
#include "main.h"
#include "stm32l0xx_hal.h"
#include <stdint.h>
#include <stdio.h>
#include <string.h>
#include "net.h"
#include "rtc.h"
#include "adc.h"//peripheral/include/
#include "protocol_common.h"
#include "net_protocol.h"
#include "auto_ctrl_task.h"
#include "bsp_board.h"
#include "lcd12864.h"
#include "uart.h"
#include "debug.h"
#include "stm32_flash.h"
#include "adc.h"
#include "rtc.h"
#include "user_config.h"
#include "../protocol/include/daoSheng_protocol.h"
#include "pin_definitions.h"

NET_INFO net_info = {0,};
float vdda_voltage = 0;
uint8_t measure_complete = 0;

// 引脚定义已移至pin_definitions.h文件中

CALENDAR calendar;

// 任务调度相关变量
uint32_t net_task_timer = 0;
uint32_t auto_ctrl_timer = 0;
uint32_t main_task_timer = 0;
uint8_t net_initialized = 0;

// 函数声明
void uart_test(void);
void daoSheng_protocol_test(void);

static void net_task_init(void)
{
	char ip[20];
	/*初始化网络模块*/
	if(net_init(&net_info,115200) == NET_ERROR_NONE)
	{
		memset(ip,0,sizeof(ip));
		sprintf(ip,"%d.%d.%d.%d",config.net_param.ip[0][0],config.net_param.ip[0][1],config.net_param.ip[0][2],config.net_param.ip[0][3]);
		if(net_info.tcpip->connect(ip, config.net_param.port[0]) == NET_ERROR_NONE)
		{
			/*连接成功，同步时间*/
			CALENDAR calendar_temp;
			memset(&calendar_temp, 0, sizeof(calendar_temp));
			if(net_info.base_function->sync_time(&calendar_temp.year, &calendar_temp.month, &calendar_temp.day, &calendar_temp.hour, &calendar_temp.minute, &calendar_temp.second) == NET_ERROR_NONE)
			{
				rtc_set_time(calendar_temp.year, calendar_temp.month, calendar_temp.day, calendar_temp.hour, calendar_temp.minute, calendar_temp.second);
			}
			net_initialized = 1;
		}
	}
}

static void net_task_process(void)
{
	if(net_initialized)
	{
		/*获取当前时间*/
		rtc_get_calendar(&calendar.year, &calendar.month, &calendar.day, &calendar.hour, &calendar.minute, &calendar.second);
		net_protocol_parsing();
	}
}

static void auto_ctrl_task_process(void)
{
	auto_ctrl();
}

/**
 * @brief 串口测试函数
 * 测试串口的初始化、发送和接收功能
 */
void uart_test(void)
{
	printf("开始串口测试...\r\n");

	// 配置串口参数（使用common.h中的protocol_config_t结构）
	protocol_config_t uart_config;
	uart_config.manufacturer = MANUFACTURER_DALIAN;  // 厂家代码：大连道盛
	uart_config.baud_code = BAUD_9600;               // 波特率：9600
	uart_config.data_bits = 8;                       // 数据位：8位
	uart_config.stop_bits = 1;                       // 停止位：1位
	uart_config.parity = PARITY_NONE;                // 校验位：无校验

	// 初始化串口
	if(uart_init(uart_config) == 0)
	{
		printf("串口初始化成功\r\n");
		printf("配置参数：波特率=%d, 数据位=%d, 停止位=%d, 校验位=%d\r\n",
			   9600, uart_config.data_bits, uart_config.stop_bits, uart_config.parity);
	}
	else
	{
		printf("串口初始化失败\r\n");
		return;
	}

	// 测试发送功能
	uint8_t test_data[] = "Hello UART Test!\r\n";
	printf("发送测试数据: %s", test_data);

	if(uart_send(test_data, strlen((char*)test_data)) == 0)
	{
		printf("数据发送成功\r\n");
	}
	else
	{
		printf("数据发送失败\r\n");
	}

	// 等待一段时间，模拟接收数据
	HAL_Delay(2000);

	// 测试接收功能
	uint8_t rx_buffer[64];
	int rx_len = uart_receive(rx_buffer, sizeof(rx_buffer));

	if(rx_len > 0)
	{
		printf("接收到数据，长度: %d\r\n", rx_len);
		printf("接收数据: ");
		for(int i = 0; i < rx_len; i++)
		{
			printf("0x%02X ", rx_buffer[i]);
		}
		printf("\r\n");
	}
	else
	{
		printf("未接收到数据\r\n");
	}

	// 测试数据可用性检查
	uint8_t available = uart_available();
	printf("缓冲区可用数据长度: %d\r\n", available);

	// 清空缓冲区
	uart_flush();
	printf("缓冲区已清空\r\n");

	printf("串口测试完成\r\n\r\n");
}

/**
 * @brief 大连道盛协议测试函数
 * 测试水表数据读取和解析功能
 */
void daoSheng_protocol_test(void)
{
	printf("开始大连道盛协议测试...\r\n");

	// 调用协议解析函数
	int result = daoSheng_protocol_parsing();

	if(result == 0) {
		printf("协议解析执行完成\r\n");

		// 检查解析结果
		if(g_water_meter_read_flag == 1) {
			printf("水表数据读取成功！\r\n");
			printf("解析结果：\r\n");
			printf("  正累计流量: %d\r\n", g_posFlow);
			printf("  负累计流量: %d\r\n", g_negFlow);
			printf("  净累计流量: %d\r\n", g_netFlow);
			printf("  瞬时流量: %.2f\r\n", g_instFlow);
			printf("  流速: %.2f\r\n", g_velocity);
			printf("  电池电压: %.2fV\r\n", g_batteryVoltage);
			printf("  ESN: %s\r\n", g_esnStr);
		} else {
			printf("水表数据读取失败\r\n");
		}
	} else {
		printf("协议解析执行失败\r\n");
	}

	printf("大连道盛协议测试完成\r\n\r\n");
}
static void MX_GPIO_Init(void) {
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();

  // 配置GPIOB输出引脚：SYS_LED (PB12), CAT1_PWR_EN (PB0), CAT1_PWR_KEY (PB1), CAT1_RST_N (PB9)
  GPIO_InitStruct.Pin = SYS_LED_PIN | CAT1_PWR_EN_PIN | CAT1_PWR_KEY_PIN | CAT1_RST_N_PIN;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(SYS_LED_PORT, &GPIO_InitStruct);

  // 配置CAT1_STATUS (PB2)为输入引脚
  GPIO_InitStruct.Pin = CAT1_STATUS_PIN;
  GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
  GPIO_InitStruct.Pull = GPIO_PULLUP;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(CAT1_STATUS_PORT, &GPIO_InitStruct);

  // 配置GPIOA输出引脚：RS485_PWR (PA11)
  GPIO_InitStruct.Pin = RS485_PWR_PIN;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(RS485_PWR_PORT, &GPIO_InitStruct);

}



int main(void)
{
	// 系统初始化
	HAL_Init();

	// 时钟配置
	system_clock_init();
	// 初始化系统tick
	HAL_SYSTICK_Config(HAL_RCC_GetHCLKFreq() / 1000);

	__HAL_RCC_PWR_CLK_ENABLE();

	/* 初始化EEPROM */
	// EEPROM_Init();
	// read_config(0);

	// 板级初始化
	bsp_board_init();

	// 使用新的串口初始化接口
	protocol_config_t uart_config;
	uart_config.manufacturer = MANUFACTURER_DALIAN;  // 默认厂家代码
	uart_config.baud_code = BAUD_9600;               // 默认波特率
	uart_config.data_bits = 8;                       // 默认数据位
	uart_config.stop_bits = 1;                       // 默认停止位
	uart_config.parity = PARITY_NONE;                // 默认校验位
	uart_init(uart_config);
	
	debug_init(115200);
	printf("debug_init初始化成功\r\n");

	rtc_init();
	printf("rtc_init初始化成功\r\n");

	// LCD初始化
	lcd_init();
	lcd_clear(0);
	ADC_Voltage_Init();
	display_flow_rate();
	printf("lcd_init初始化成功\r\n");
	// GPIO初始化
	MX_GPIO_Init();

	// 初始化各模块电源和复位状态
	CAT1_POWER(1);
	CAT1_RST(1);  // CAT1模块复位引脚初始化为高电平（非复位状态）
	RS485_PWR(1); // RS485电源控制初始化为高电平（上电）
	HAL_Delay(500);
	CAT1_POWER(0);

	// 网络初始化
	net_task_init();

	uint32_t CR_time = HAL_GetTick();
	printf("CR_Time = %d\r\n", CR_time);

	// 初始化任务定时器
	net_task_timer = HAL_GetTick();
	auto_ctrl_timer = HAL_GetTick();
	main_task_timer = HAL_GetTick();

	// 串口测试函数调用
	// uart_test();



	while(1)
	{
		uint32_t current_time = HAL_GetTick();

//		// 网络任务处理 - 每100ms执行一次
//		if(current_time - net_task_timer >= 100)
//		{
//			net_task_process();
//			net_task_timer = current_time;
//		}

//		// 自动控制任务处理 - 每500ms执行一次
//		if(current_time - auto_ctrl_timer >= 500)
//		{
//			auto_ctrl_task_process();
//			auto_ctrl_timer = current_time;
//		}

		// 主任务处理 - 每5000ms执行一次
		if(current_time - main_task_timer >= 2000)
		{
			// protocol_parsing();
			HAL_GPIO_TogglePin(GPIOB, GPIO_PIN_12);

			// vdda_voltage = Get_Power_Voltage();
			// display_rotate();
				// 大连道盛协议测试函数调用
			daoSheng_protocol_test();
			// printf("电源电压 = %.2f\r\n", vdda_voltage);
			main_task_timer = current_time;
		}

		// 处理串口超时
		uart_timeout_handler();

		// 短暂延时，避免CPU占用过高
		HAL_Delay(1);
	}
}
