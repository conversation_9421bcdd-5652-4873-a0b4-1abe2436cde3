# STM32L072CBT6 MCU 系统完整需求与 AI 编程任务

---

## 1️⃣ MCU 引脚分配与功能说明

### 1.1 Cat1 模块接口（EC600S）

| 信号名称           | MCU 引脚 | 方向 | 说明 |
| -------------- | ------ | -- | -- |
| CAT1\_UART\_TX |        |    |    |

| **PA10**       | 输出      | MCU → Cat1 模块主串口 TX |                               |
| -------------- | ------- | ------------------- | ----------------------------- |
| CAT1\_UART\_RX | **PA9** | 输入                  | Cat1 模块 → MCU 主串口 RX          |
| CAT1\_PWR\_EN  | PB0     | 输出                  | 控制 Cat1 模块电源开关 MOSFET，高电平上电   |
| CAT1\_PWR\_KEY | PB1     | 输出                  | 控制模块 PWRKEY，低电平保持约 1.5 秒启动/关机 |
| CAT1\_STATUS   | PB2     | 输入                  | Cat1 模块状态检测，高电平表示模块启动完成       |
| CAT1\_REST\_N  | **PB9** | 输出                  | Cat1 模块硬件复位引脚（低电平复位）          |

---

### 1.2 LCD 显示接口（SPI 控制 ST7567）

| 信号名称         | MCU 引脚 | 方向 | 说明              |
| ------------ | ------ | -- | --------------- |
| LCD\_MOSI    | PB3    | 输出 | SPI MOSI        |
| LCD\_SCK     | PB4    | 输出 | SPI 时钟          |
| LCD\_A0      | PB5    | 输出 | 数据/命令选择         |
| LCD\_RST     | PB6    | 输出 | LCD 复位          |
| LCD\_CS      | PB7    | 输出 | SPI 片选          |
| LCD\_PWR\_EN | PA12   | 输出 | LCD 电源控制（高电平关闭） |

---

### 1.3 RS485 通信接口（UART2）

| 信号名称          | MCU 引脚 | 方向 | 说明             |
| ------------- | ------ | -- | -------------- |
| RS485\_TX     | PA2    | 输出 | 向水表发送数据        |
| RS485\_RX     | PA3    | 输入 | 从水表接收数据        |
| RS485\_TX\_EN | PB8    | 输出 | 485 驱动方向，高电平发送 |
| RS485\_PWR    | PA11   | 输出 | RS485 电源控制     |

---

### 1.4 按键与唤醒接口

| 信号名称      | MCU 引脚 | 方向 | 说明                |
| --------- | ------ | -- | ----------------- |
| WAKE\_KEY | PC13   | 输入 | 唤醒/功能按键（EXTI中断唤醒） |

---

### 1.5 电池监测与调试串口

| 信号名称            | MCU 引脚  | 方向 | 说明                   |
| --------------- | ------- | -- | -------------------- |
| BAT\_ADC        | PA4     | 输入 | 电池电压采集 ADC 通道        |
| DEBUG\_UART\_TX | **PA0** | 输出 | 调试串口 TX（UART1/USART） |
| DEBUG\_UART\_RX | **PA1** | 输入 | 调试串口 RX（UART1/USART） |

---

### 1.6 系统状态指示灯

| 信号名称     | MCU 引脚   | 方向 | 说明             |
| -------- | -------- | -- | -------------- |
| SYS\_LED | **PB12** | 输出 | 系统运行状态灯（高电平点亮） |

---

## 2️⃣ 功能需求概述

### 2.1 数据采集功能

* MCU 通过 RS485 接口采集水表数据
* 激活状态下每 1 秒采集一次

### 2.2 显示功能

* 默认熄屏
* 干簧管触发或按键触发唤醒 LCD 显示
* RTC 定时唤醒不显示内容

### 2.3 按键控制

* 按键触发唤醒 LCD 并显示数据
* 按键激活状态可立即触发数据上报

### 2.4 配置模式机制

* 上电 1 秒为配置窗口
* RS485 接收到合法配置命令进入配置模式
* 否则转入采集发送状态，发送完成后进入低功耗

### 2.5 采集模式逻辑

#### 激活状态

* 每 1 秒采集一次水表数据并通过 Cat1 上传
* 按键触发 LCD 显示内容，RTC 唤醒不显示

#### 休眠状态

* 从 Standby、RTC 或外部按键唤醒
* 暂停数据采集，仅 RTC 或按键可唤醒

---

## 3️⃣ AI 编程任务模块拆解

### 3.1 硬件初始化

**任务**: GPIO、UART、SPI、ADC、RTC 初始化
**验证**: LED 点亮、UART 回环、ADC 输出

### 3.2 RS485 水表采集模块

**任务**: RS485 发送/接收、数据解析、CRC 校验
**验证**: 模拟水表发送数据，解析 CRC 正确，打印调试串口

### 3.3 Cat1 数据上报模块

**任务**: UART 控制 Cat1、PWRKEY 上电、发送数据
**验证**: AT 命令 OK、数据上传平台、STATUS 高电平

### 3.4 LCD 显示模块

**任务**: SPI LCD 显示、按键唤醒、休眠熄屏
**验证**: 按键触发显示，RTC 唤醒不显示，显示数据正确

### 3.5 按键与唤醒控制

**任务**: EXTI 中断，按键触发 LCD 显示和数据上报
**验证**: 按键唤醒 MCU，LCD 点亮，数据上报

### 3.6 配置模式处理

**任务**: 上电 1 秒配置窗口，RS485 接收配置命令，保存 Flash
**验证**: 配置命令生效，复位后仍有效

### 3.7 休眠与 RTC 定时唤醒

**任务**: Standby 模式、RTC 4 小时唤醒、按键外部唤醒
**验证**: RTC 唤醒采集正常、按键唤醒有效、功耗最低

---

## 4️⃣ AI 编程提示

### 4.1 模块化函数封装

```c
init_hardware();
read_water_meter();
send_to_platform(data);
lcd_show_data(data);
key_pressed();
enter_low_power();
handle_config_mode();
```

### 4.2 使用已有 demo 协议

* RS485 demo → 水表数据解析
* 平台协议 demo → Cat1 上传数据
* 配置协议 demo → MCU 配置处理

### 4.3 验证代码

* 每个模块独立测试
* 模拟输入，输出到调试串口或 LCD
* 可在开发板或 HAL 模拟器验证

**示例测试流程：**

```text
1. 初始化硬件（GPIO、UART、SPI、ADC）
2. 模拟 RS485 水表发送数据
3. 调用 RS485 demo 解析数据
4. 调用平台协议 demo 上传数据
5. 模拟按键触发 LCD 显示
6. 检查输出与预期是否一致
```

---

## 5️⃣ 调用顺序示意图

```text
                 +--------------------+
                 |  MCU 上电/复位     |
                 +---------+----------+
                           |
                           v
                 +--------------------+
                 | init_hardware()    |
                 +---------+----------+
                           |
          +----------------+----------------+
          |                                 |
          v                                 v
 +--------------------+           +--------------------+
 | handle_config_mode()|<-------->|  RS485 接收配置命令 |
 +--------------------+           +--------------------+
          |
          v
 +--------------------+
 | enter_low_power()   |
 | (Standby, RTC sleep)|
 +---------+----------+
           |
           v
  +------------------------+
  | RTC/按键唤醒触发      |
  +----------+-------------+
             |
             v
   +---------------------+
   | read_water_meter()  |
   +----------+----------+
             |
             v
   +---------------------+
   | send_to_platform()  |
   +----------+----------+
             |
             v
   +---------------------+
   | lcd_show_data()     |
   +----------+----------+
             |
             v
   +---------------------+
   | enter_low_power()   |
   +---------------------+
             |
             v
        循环等待下一次 RTC/按键唤醒
```

---

## 5.1 验证流程

1. **硬件初始化验证**

   * LED 点亮
   * UART 回环
   * ADC 电压读取

2. **RS485 数据采集验证**

   * 模拟水表发送数据
   * CRC 校验正确
   * 串口打印采集值

3. **Cat1 上传验证**

   * AT 命令 OK
   * 数据上传平台成功
   * STATUS 高电平

4. **LCD 显示验证**

   * 按键触发 LCD 点亮
   * RTC 唤醒不显示
   * 显示数据与采集一致

5. **配置模式验证**

   * 上电 1 秒内配置命令生效
   * 保存 Flash 配置
   * 复位后配置有效

6.

## **休眠/唤醒验证**
