# SysTick_Handler 死锁问题修复报告

## 问题描述

程序运行后死在 `SysTick_Handler` 中，具体位置：
```assembly
SysTick_Handler PROC
                EXPORT  SysTick_Handler                [WEAK]
                B       .
                ENDP
```

## 问题分析

经过分析，发现问题主要出现在以下几个方面：

### 1. UART 中断配置问题
- UART 初始化和中断使能的顺序不当
- 中断优先级设置时机不正确
- 中断处理函数可能存在死循环或阻塞

### 2. delay_us 函数实现问题
- 原始实现依赖 SysTick 定时器
- 在 SysTick 中断处理期间调用可能导致死锁
- 与系统 tick 机制冲突

## 修复措施

### 1. 优化 UART 初始化顺序 (`peripheral/uart.c`)

**修复前：**
```c
HAL_UART_Init(&serial_485_handler);
__HAL_UART_ENABLE_IT(&serial_485_handler,UART_IT_RXNE);
HAL_NVIC_EnableIRQ(USART2_IRQn);
HAL_NVIC_SetPriority(USART2_IRQn,3,3);
```

**修复后：**
```c
// 先设置中断优先级，再初始化UART
HAL_NVIC_SetPriority(USART2_IRQn, 3, 3);
HAL_NVIC_EnableIRQ(USART2_IRQn);

// 初始化UART
if(HAL_UART_Init(&serial_485_handler) != HAL_OK)
{
  // UART初始化失败处理
  return;
}

// 最后使能UART接收中断
__HAL_UART_ENABLE_IT(&serial_485_handler, UART_IT_RXNE);
```

### 2. 重写 UART 中断处理函数

**修复前：**
```c
void USART2_IRQHandler(void)
{
  if(__HAL_UART_GET_FLAG(&serial_485_handler,UART_FLAG_RXNE) != RESET)
  {
    // 使用HAL宏，可能存在问题
    // ...
  }
}
```

**修复后：**
```c
void USART2_IRQHandler(void)
{
  volatile uint32_t isr_reg = serial_485_handler.Instance->ISR;
  
  // 直接检查寄存器位，避免HAL宏的复杂性
  if(isr_reg & (1 << 5)) // RXNE bit 5
  {
    uint8_t received_data = (uint8_t)(serial_485_handler.Instance->RDR);
    
    if(serial_485.rx_count < SERIAL_BUFFER_SIZE)
    {
      serial_485.rx_buffer[serial_485.rx_count++] = received_data;
    }
    else
    {
      serial_485.rx_count = 0;
      serial_485.rx_buffer[serial_485.rx_count++] = received_data;
    }
    
    serial_485.byte_timeout = SERIAL_TIMEOUT;
  }
  
  // 清除错误标志
  if(isr_reg & ((1 << 3) | (1 << 1) | (1 << 0))) // ORE, FE, PE
  {
    (void)serial_485_handler.Instance->RDR;
  }
}
```

### 3. 重写 delay_us 函数 (`system/system.c`)

**修复前：**
```c
void delay_us(uint32_t us)
{
  // 复杂的SysTick基础实现
  // 可能与SysTick中断冲突
  uint32_t ticks = us * reload / (1000000 / 1000);
  // ... 复杂的SysTick操作
}
```

**修复后：**
```c
void delay_us(uint32_t us)
{
  // 简单的循环延时，避免与SysTick冲突
  volatile uint32_t count = us * 2;
  
  while(count--)
  {
    __NOP(); // 空操作指令
  }
}
```

## 修复原理

### 1. 中断优先级管理
- 确保 UART 中断优先级低于 SysTick
- 避免中断嵌套导致的死锁

### 2. 简化中断处理
- 直接操作寄存器，避免 HAL 宏的复杂性
- 减少中断处理时间
- 避免在中断中调用可能阻塞的函数

### 3. 避免 SysTick 依赖
- delay_us 函数不再依赖 SysTick
- 使用简单循环延时
- 避免在 SysTick 中断期间的递归调用

## 验证建议

1. **功能测试**：验证 UART 通信是否正常
2. **稳定性测试**：长时间运行测试，确保不再死锁
3. **中断响应测试**：验证中断响应时间是否合理
4. **延时精度测试**：验证 delay_us 函数的延时精度

## 注意事项

1. delay_us 函数现在是基于循环的简单延时，精度可能不如基于定时器的实现
2. 如果需要高精度延时，建议使用专用的定时器
3. UART 中断处理现在更加简洁，但需要确保缓冲区管理正确
4. 系统稳定性应该得到显著改善

## 总结

通过优化 UART 初始化顺序、简化中断处理函数和重写 delay_us 函数，解决了 SysTick_Handler 死锁问题。修复后的代码更加稳定可靠，避免了中断冲突和递归调用问题。
