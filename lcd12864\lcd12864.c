#include "lcd12864.h"
#include "lcd_font.h"
#include "adc.h"
#include "rtc.h"
#include "user_config.h"
#include <string.h>
#include <stdio.h>
#include "daoSheng_protocol.h"

#include "pin_definitions.h"

// LCD控制宏定义已移至pin_definitions.h
#define LCD_BACKLIGHT(x) HAL_GPIO_WritePin(GPIOA, GPIO_PIN_5, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)

#define LCD_POWER_ON_OFF(x) HAL_GPIO_WritePin(GPIOA, GPIO_PIN_12, (x) ? GPIO_PIN_SET : GPIO_PIN_RESET)

static uint8_t lcd_ram[128][8]; /*屏幕整屏缓存*/
 RTC_HandleTypeDef  hrtc;  // 添加RTC句柄外部声明RTC_HandleTypeDef 
 // 电池图标字模（4级电量）
const uint8_t battery_icons[4][8] = {
    {0xFF, 0x81, 0x81, 0x81, 0x81, 0x81, 0x81, 0xFF}, // 空电
    {0xFF, 0x81, 0x81, 0x81, 0x81, 0x81, 0xBD, 0xFF}, // 1格
    {0xFF, 0x81, 0x81, 0xBD, 0xBD, 0xBD, 0xBD, 0xFF}, // 2格
    {0xFF, 0x81, 0xBD, 0xBD, 0xBD, 0xBD, 0xBD, 0xFF}  // 满电
};

/**
 * @brief 初始化 LCD GPIO 引脚
 */
static void lcd_gpio_init(void)
{
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_GPIOB_CLK_ENABLE();

  GPIO_InitTypeDef gpio;

  // 配置 GPIO 引脚
  gpio.Mode = GPIO_MODE_OUTPUT_PP;
  gpio.Pull = GPIO_NOPULL;
  gpio.Speed = GPIO_SPEED_FREQ_HIGH;

  // 初始化 LCD SPI 引脚：CS, RST, A0, CLK, SDA
  gpio.Pin = LCD_CS_PIN | LCD_RST_PIN | LCD_A0_PIN | LCD_SCK_PIN | LCD_MOSI_PIN;
  HAL_GPIO_Init(LCD_CS_PORT, &gpio);

  // 初始化背光控制引脚
  gpio.Pin = GPIO_PIN_5;
  HAL_GPIO_Init(GPIOA, &gpio);

  // 初始化LCD电源控制引脚
  gpio.Pin = LCD_PWR_EN_PIN;
  HAL_GPIO_Init(LCD_PWR_EN_PORT, &gpio);
}

/**
 * @brief 写入命令到 LCD
 * @param command 命令字节
 */
static void lcd_write_command(uint8_t command)
{
  LCD_CS(0);
  LCD_A0(0);
  for (uint8_t i = 0; i < 8; i++)
  {
    LCD_SDA((command & 0x80) ? 1 : 0);
    LCD_CLK(0);
    LCD_CLK(1);
    command <<= 1;
  }
  LCD_CS(1);
}

/**
 * @brief 写入数据到 LCD
 * @param data 数据字节
 */
static void lcd_write_data(uint8_t data)
{
  LCD_CS(0);
  LCD_A0(1);
  for (uint8_t i = 0; i < 8; i++)
  {
    LCD_SDA((data & 0x80) ? 1 : 0);
    LCD_CLK(0);
    LCD_CLK(1);
    data <<= 1;
  }
  LCD_CS(1);
}
 


void lcd_init(void)
{

  lcd_gpio_init();
  LCD_PWR_EN(0); // 使用新的引脚定义宏，低电平开启LCD电源
  memset(lcd_ram, 0, sizeof(lcd_ram));
  LCD_RST(1);
  delay_us(30);
  LCD_RST(0);
  delay_us(30);
  LCD_RST(1);
  delay_us(100);
  lcd_write_command(0xE2);
  delay_us(10);
	lcd_write_command(0xE0);
  lcd_write_command(0xA0); // 0xa2设置偏压比为1/9
  lcd_write_command(0xA0); // 0xA0设置列地址从00H开始
  lcd_write_command(0xC8); // 0xc8设置com扫描方向，从COM(n-1)到 COM0
  lcd_write_command(0x23); // vop粗调
  lcd_write_command(0x81); // vop双指令
  lcd_write_command(0x32); // vop微调
  lcd_write_command(0x2F); // 0x2f电源状态、输出缓冲开、内部电压调整开，电压调节开关
  lcd_write_command(0xB0); // 0xb0设置页列地址
  lcd_write_command(0xAF); // 0xaf设置显示LCD开关
  lcd_write_command(0xA6); // 0xa6设置正常显开关

  lcd_clear(0);
  lcd_refresh();
}

void lcd_refresh(void)
{
  for (uint8_t i = 0; i < 8; i++)
  {
    lcd_write_command(0x00);
    lcd_write_command(0xb0 + i); // Set page address
    lcd_write_command(0x00);     // Set column address (LSB)
    lcd_write_command(0x10);     // Set column address (MSB)
    for (uint8_t j = 0; j < 128; j++)
    {
      lcd_write_data(lcd_ram[j][i]);//
    }
  }
 
}
/**
 * mode:0:清屏，1全显
 */
void lcd_clear(uint8_t mode)
{
for (uint8_t i = 0; i < 9; i++)
{
	for (uint8_t j = 0; j < 128; j++)
	{
		lcd_ram[j][i] = (mode == 0) ? 0x00 : 0xff;
	}
}
}

// 静态函数，用于在LCD上绘制一个像素点
static void lcd_draw_pixel(uint8_t x, uint8_t y, uint8_t mode)
{
  // 如果像素点的x坐标或y坐标超出了LCD的宽度或高度，则直接返回
  if ((x >= LCD_WIDTH) || (y >= LCD_HEIGHT))
  {
    return;
  }

  // 定义变量pos、bx和temp，用于计算像素点在LCD RAM中的位置
  uint8_t pos = 0, bx = 0, temp = 0;
  // 计算像素点在LCD RAM中的行号
  pos = y / 8;
  // 计算像素点在LCD RAM中的列号
  bx = y % 8;
  // 计算像素点在LCD RAM中的位置
  temp = (1 << (7 - bx));

  // 根据mode参数，将像素点绘制在LCD RAM中
  if (mode)
    lcd_ram[x][pos] |= temp;
  else
    lcd_ram[x][pos] &= ~temp;
}

/**
 * @brief
 *
 * @param s_x 起始位置
 * @param e_x 结束位置
 * @param s_y
 * @param e_y
 * @param mode 0正常 1反显
 */
/**
* @brief 在LCD上绘制一条线
*
* 使用给定的起点和终点坐标在LCD上绘制一条线。
*
* @param s_x 线的起点x坐标
* @param e_x 线的终点x坐标
* @param s_y 线的起点y坐标
* @param e_y 线的终点y坐标
* @param mode 绘制模式，通常用于指定线条颜色或风格
*/
void lcd_draw_line(uint8_t s_x, uint8_t e_x, uint8_t s_y, uint8_t e_y, uint8_t mode)
{
  if ((s_x > LCD_WIDTH) || (s_y > LCD_HEIGHT))
    return;
  if ((e_x > LCD_WIDTH) || (e_y > LCD_HEIGHT))
    return;

  for (uint8_t i = s_y; i < (e_y + 1); i++)
  {
    for (uint8_t j = s_x; j < (e_x + 1); j++)
    {
      lcd_draw_pixel(j, i, mode);
    }
  }
}

// /**
// * @brief 在LCD屏幕上显示字符
// *
// * 根据给定的字符数据、位置、数量、模式和字体类型，在LCD屏幕上显示字符。
// *
// * @param x 显示字符的起始横坐标
// * @param y 显示字符的起始纵坐标
// * @param dat 指向字符数据的指针
// * @param count 要显示的字符数量
// * @param mode 显示模式，通常用于控制前景色和背景色
// * @param font_type 字体类型，FONT_1616B 或 FONT_2424B
// */
 static void lcd_show_char(uint8_t x, uint8_t y, uint8_t *dat, uint8_t count, uint8_t mode, FONT_TYPE font_type)
 {
   // 初始化临时变量和起始行坐标
   uint8_t temp = 0, y_start = y;
   uint8_t font_size = 0;

   // 根据字体类型设置字体大小
   if (font_type == FONT_0808B)
     font_size = 8;
   else if (font_type == FONT_1616B)          
     font_size = 16;
   else if (font_type == FONT_2424B)
     font_size = 24;
   else
     return; // 如果字体类型不匹配，则直接返回

   // 遍历每个字符
   for (uint8_t i = 0; i < count; i++)
   {
     temp = dat[i]; // 获取当前字符的数据

     // 遍历字符的每一个像素点
     for (int8_t j = 0; j < 8; j++)
     {
       // 根据当前像素点的值绘制像素
       lcd_draw_pixel(x, y, (temp & 0x80) ? (mode) : (!mode));
       temp <<= 1; // 左移一位，检查下一个像素点
       y++; // 移动到下一行

       // 如果当前行已经超出字体高度，则回到起始行并向右移动一列
       if ((y - y_start) == font_size)
       {
         y = y_start;
         x++;
         break;
       }
     }
   }
 }

/**
* @brief 在LCD上显示字符串
*
* 在LCD上指定位置显示字符串。
*
* @param x 开始显示字符串的横坐标
* @param y 开始显示字符串的纵坐标
* @param str 需要显示的字符串
* @param mode 显示模式
* @param font_type 字体类型
*/
void lcd_show_string(uint8_t x, uint8_t y, char *str, uint8_t mode, FONT_TYPE font_type)
{
    // 获取字体类型
    FONT_TYPE _font_type_ = get_font_type(font_type);

    // 遍历字符串直到遇到字符串结束符'\0'
    while (*str != '\0') {
        // 判断是否为中文字符（GB2312编码）
        if ((uint8_t)*str >= 0xA1 && (uint8_t)*str <= 0xF7) {
            // 中文字符占用2字节
            uint8_t *font_dat = NULL;
            uint8_t font_dat_len = 0;
            uint8_t str_offset = get_font_info((uint8_t *)str, &font_dat, &font_dat_len, _font_type_);

            if (str_offset == 0) {
                str++;
                continue;
            }

            // 显示中文字符
            lcd_show_char(x, y, font_dat, font_dat_len, mode, _font_type_);
            x += 16; // 中文字符宽度为16像素
            str += 2; // 跳过2字节的GB2312编码
        } else {
            // 英文字符或其他单字节字符
            uint8_t *font_dat = NULL;
            uint8_t font_dat_len = 0;
            uint8_t str_offset = get_font_info((uint8_t *)str, &font_dat, &font_dat_len, _font_type_);

            if (str_offset == 0) {
                str++;
                continue;
            }

            // 显示英文字符
            lcd_show_char(x, y, font_dat, font_dat_len, mode, _font_type_);
						if(_font_type_ == FONT_0808B)x += 4;    						
						else if(_font_type_ == FONT_1616B)x += 8; // 英文字符宽度为8像素
						else if(_font_type_ == FONT_2424B)x+=12;
            str += 1; // 跳过1字节的ASCII编码
        }

        // 如果x坐标超出LCD宽度，则跳出循环
        if (x > LCD_WIDTH)
            break;
    }
}

void lcd_backlight_ctrl(uint8_t opt)
{
  LCD_BACKLIGHT(opt);
}

/**
 * @brief 在右上角显示电池电压图标
 */
 void display_battery_voltage(void)
{
    float voltage = Get_Power_Voltage(); // 获取电池电压
    char voltage_str[10];
    
    // 根据电压值计算电量等级（0-3）
//    uint8_t battery_level = 0;
//    if (voltage >= 3.4f) battery_level = 3;      // 3.4V-3.6V: 满电
//    else if (voltage >= 3.0f) battery_level = 2; // 3.0V-3.4V: 2格
//    else if (voltage >= 2.5f) battery_level = 1;  // 2.5V-3.0V: 1格
//    else battery_level = 0;                      // 2.0V-2.5V: 空电
//    
//    // 显示电池图标
//    lcd_show_char(0, 0, (uint8_t *)battery_icons[battery_level], 8, 1,FONT_0808B);


    
    // 显示电压值
    sprintf(voltage_str, "%.1fV", voltage);
    lcd_show_string(8, 0, voltage_str, 1, FONT_1616B);

	//  lcd_refresh();
}

/**
 * @brief 在左上角显示4G信号强度图标
 */
static void display_signal_strength(uint8_t Signal_Strength)
{
    uint8_t signal_strength = Signal_Strength; // 获取信号强度（0-32）
    uint8_t bars = (signal_strength + 3) / 8; // 将0-32映射到0-4格
    
    // 绘制4G标识
    lcd_show_string(96, 0, "4G", 1, FONT_0808B);

    
    // 绘制水平信号强度条（从右向左）
    for (uint8_t i = 0; i < 4; i++)
    {
        if (i < bars)
        {
            // 参数说明：x起始位置，y起始位置，方向(0=水平)，长度，颜色
            lcd_draw_line(120 - i * 6, 16, 0, 6, 1);
        }
    }
}

void display_fourG_volage(void)
{
	 display_battery_voltage();
	 display_signal_strength(32);
}
   
/****************************************************/
void disp_col(void) // 横条
{
  unsigned char i, j;
  for (i = 0; i < 9; i++)
  {
    lcd_write_command(0x00);
    lcd_write_command(0xb0 + i);
    lcd_write_command(0x00);
    lcd_write_command(0x10);
    for (j = 0; j < 132; j++)
      lcd_write_data(0x00);
  }
}
/****************************************************/
void disp_row(void) // 竖条
{
  uint8_t i, j;
  for (i = 0; i < 9; i++)
  {
    lcd_write_command(0xb0 + i);
    lcd_write_command(0x00);
    lcd_write_command(0x10);
    for (j = 0; j < 128; j++)
    {
      lcd_write_data(0xFF);
      lcd_write_data(0x00);
    }
  }
}
uint8_t  str2[6]={0x3e,0x51,0x49,0x45,0x3e,0x00};//disp"0"
uint8_t  str3[6]={0x7f,0x40,0x40,0x40,0x40,0x00};//disp"L"

 /****************************************************/
 void dispstr(uint8_t d)//disp"0",disp"L"
	{
	 uint8_t i,j,k;
	 for(i=0;i<9;i++)
	{
	 lcd_write_command(0xb0+i);
	 lcd_write_command(0x00);
	 lcd_write_command(0x10);
	 for(j=0;j<22;j++)
	 if(d==0)
	{
	 for(k=0;k<6;k++)
	 lcd_write_data(str2[k]);
	}
	 else
	 for(k=0;k<6;k++)
	 lcd_write_data(str3[k]);
        }
	}


  


 void display_power_voltage(void) 
	 {
    float voltage = Get_Power_Voltage(); // 使用全局变量Get_Power_Voltage
    char voltage_str[20];
    sprintf(voltage_str, "电池电压: %.2fV", voltage); // 格式化字符串
    lcd_show_string(0, 0, (char *)voltage_str, 1, FONT_1616B); // 显示在左上角lcd_show_char
//	  lcd_refresh();
}


 void display_flow_rate(void)
{
  	char  voltage_str[20];
	  float flow_rate = 10.4f;
 //    char dateStr[20];
//      RTC_TimeTypeDef currentTime;
//      RTC_DateTypeDef currentDate;   
        /* 原子性读取时间和日期 */
//      HAL_RTC_GetTime(&hrtc, &currentTime, RTC_FORMAT_BIN);
//      HAL_RTC_GetDate(&hrtc, &currentDate, RTC_FORMAT_BIN);       
//      lcd_clear(0); //清屏
        /* 格式化日期字符串 */
//      sprintf(dateStr, "20%02d-%02d-%02d-%02d:%02d:%02d", //
//               currentDate.Year, 
//               currentDate.Month, 
//               currentDate.Date,
//               currentTime.Hours, 
//               currentTime.Minutes, 
//               currentTime.Seconds
//                );
////    flow_rate = Get_Power_Voltage(); // 使用全局变量Get_Power_Voltage
////    sprintf(voltage_str, "电池电压: %.2fV", flow_rate); // 格式化字符串
//    lcd_show_string(0, 0, (char *)dateStr, 1, FONT_0808B); // 显示在左上角lcd_show_char

	 display_battery_voltage();
   // display_signal_strength(30); //display_fourG_volage();
	  flow_rate = 1.2f;
    sprintf(voltage_str, "正累积: %.1fm3", flow_rate); 
	  flow_rate = 1.2f;
    sprintf(voltage_str, "正累积: %.1fm3", flow_rate); // 格式化字符串
    lcd_show_string(0, 16, (char *)voltage_str, 1, FONT_1616B); // 显示在左上角lcd_show_char
		flow_rate = 2.8f;
    sprintf(voltage_str, "瞬时流量: %.1fm3", flow_rate); // 格式化字符串
    lcd_show_string(0, 32, (char *)voltage_str, 1, FONT_1616B); // 显示在左上角lcd_show_char
	//  flow_rate = dpload_data.streamflow ;
    sprintf(voltage_str, "流速:%.2fm3/s", flow_rate); // 格式化字符串
	  lcd_show_string(0, 48, (char *)voltage_str, 1, FONT_1616B); // 显示在左上角lcd_show_char
	  lcd_refresh();
}



 static void display_flow_rate1(void)
{
  	char  voltage_str[20];
    char  dateStr[100];
	  float flow_rate = 10.4f;    
    sprintf(voltage_str, "负累积: %.1fm/s", flow_rate); // 格式化字符串
    lcd_show_string(0, 0, (char *)voltage_str, 1, FONT_1616B); // 显示在左上角lcd_show_char
	
	  sprintf(voltage_str, "终端ID: %02X%02X%02X%02X%02X", config.Terminal_ID[0], config.Terminal_ID[1], config.Terminal_ID[2], config.Terminal_ID[3], config.Terminal_ID[4]);  
    lcd_show_string(0, 16, (char *)voltage_str, 1, FONT_1616B); // 显示在左上角lcd_show_char

	  flow_rate = Get_Power_Voltage(); // 使用全局变量Get_Power_Voltage
	  sprintf(voltage_str, "基表ID: %.2fV", flow_rate); // 格式化字符串
    lcd_show_string(0, 32, (char *)voltage_str, 1, FONT_1616B); // 显示在左上角lcd_show_char
	  strcpy(dateStr, " 成功");
    snprintf(voltage_str, sizeof(voltage_str), "数据上报:成功%s", dateStr);//sprintf(voltage_str, "数据上报: %s", dateStr); // 格式化字符串
    lcd_show_string(0, 48, (char *)voltage_str, 1, FONT_1616B); // 显示在左上角lcd_show_char	  
	  lcd_refresh();
	
}


/* 轮换显示状态机 */
uint8_t display_state ;
static uint32_t last_switch_time = 0;

void display_rotate(void)
{
    uint32_t current_time = HAL_GetTick();
    
    // 每3秒切换一次显示
    if(current_time - last_switch_time >= 3000)
    {
        last_switch_time = current_time;
			 
        
        if(display_state)
        {
					 display_state = 0;
           display_flow_rate();
          // printf("laste = %d\r\n", last_switch_time);
				}
				else{
					      display_state=1;
                display_flow_rate1();
               
				    //	printf("last_switch_time = %d\r\n", last_switch_time);     
                
        }
    }
		
}

