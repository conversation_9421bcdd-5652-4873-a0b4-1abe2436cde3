/**
 * @file protocol.c
 * @brief 水表采集器配置工具通信协议 - STM32设备端实现
 * <AUTHOR> Assistant
 * @date 2025-08-26
 * 
 * 协议特点：
 * - 基于串口通信（UART）
 * - 采用固定帧格式：0x7E + 长度 + 命令码 + 数据 + 0xCE
 * - 小端序数据传输
 * - 除升级功能外，其他功能不使用CRC校验
 */

#include <stdint.h>
#include <string.h>
#include "protocol.h"


// 协议帧格式定义
#define FRAME_HEAD          0x7E
#define FRAME_TAIL          0xCE

// 命令码定义
#define CMD_HEARTBEAT       0x02    // 连接握手/心跳
#define CMD_SYS_VERSION     0x05    // 查询系统版本
#define CMD_CENTER1         0x11    // 中心1配置
#define CMD_CENTER2         0x12    // 中心2配置  
#define CMD_CENTER3         0x13    // 中心3配置
#define CMD_PROTOCOL        0x15    // 厂家协议配置
#define CMD_OTHER_CONFIG    0x16    // 其他配置
#define CMD_TERMINAL_ID     0x17    // 设置终端ID
#define CMD_READ_TERMINAL_ID 0x18   // 读取终端ID
#define CMD_UPDATE          0x06     // 升级，不用响应

// 响应码定义
#define RESP_HEARTBEAT      0x82    // 连接确认响应
#define RESP_CONFIG_OK      0x83    // 配置设置成功响应
#define RESP_SYS_VERSION    0x85    // 系统版本响应
#define RESP_CENTER1        0x91    // 中心1响应
#define RESP_CENTER2        0x92    // 中心2响应
#define RESP_CENTER3        0x93    // 中心3响应
#define RESP_PROTOCOL       0x95    // 厂家协议响应
#define RESP_OTHER_CONFIG   0x96    // 其他配置响应
#define RESP_TERMINAL_ID    0x97    // 终端ID响应

// 厂家代码定义
#define MANUFACTURER_DALIAN     1   // 大连道盛
#define MANUFACTURER_TAIAN      2   // 泰安
#define MANUFACTURER_TANGSHAN   3   // 唐山
#define MANUFACTURER_HENAN      4   // 河南

// 波特率代码定义
#define BAUD_1200           1
#define BAUD_2400           2
#define BAUD_4800           3
#define BAUD_9600           4
#define BAUD_19200          5
#define BAUD_38400          6

// 校验位定义
#define PARITY_NONE         0   // 无校验
#define PARITY_ODD          1   // 奇校验
#define PARITY_EVEN         2   // 偶校验

// 数据兼容模式定义
#define DATA_COMPAT_OFF     0   // 关闭
#define DATA_COMPAT_ON      1   // 开启

// 配置数据结构定义
typedef struct {
    uint8_t ip[4];              // IP地址
    uint16_t port;              // 端口号（小端序）
    uint16_t period;            // 上报周期，单位分钟（小端序）
} center_config_t;

typedef struct {
    uint8_t manufacturer;       // 厂家代码
    uint8_t baud_code;         // 波特率代码
    uint8_t data_bits;         // 数据位
    uint8_t stop_bits;         // 停止位
    uint8_t parity;            // 校验位
} protocol_config_t;

typedef struct {
    uint8_t data_compat_mode;   // 数据兼容模式
} other_config_t;

typedef struct {
    uint8_t id[5];             // 终端ID，5字节
} terminal_id_t;

// 全局配置变量（需要在实际应用中持久化存储）
static center_config_t center1_config = {{192, 168, 1, 100}, 8016, 1};
static center_config_t center2_config = {{10, 0, 0, 1}, 9000, 2};
static center_config_t center3_config = {{172, 16, 1, 10}, 8888, 3};
static protocol_config_t protocol_config = {MANUFACTURER_DALIAN, BAUD_9600, 8, 1, PARITY_NONE};
static other_config_t other_config = {DATA_COMPAT_OFF};
static terminal_id_t terminal_id = {{0x65, 0x32, 0x23, 0x2D, 0x92}};
static uint8_t system_version[3] = {'1', '2', '3'};  // 版本V1.2.3

// 外部函数声明（需要用户实现）
extern void uart_send_data(uint8_t *data, int length);

/**
 * @brief 发送响应数据包
 * @param resp_code 响应码
 * @param data 数据指针
 * @param data_len 数据长度
 */
static void send_response(uint8_t resp_code, uint8_t *data, uint8_t data_len)
{
    uint8_t response[64];
    int index = 0;

    response[index++] = FRAME_HEAD;                    // 帧头
    response[index++] = data_len + 2;                  // 长度（长度字段本身+响应码+数据）
    response[index++] = resp_code;                     // 响应码

    if (data && data_len > 0) {
        memcpy(&response[index], data, data_len);      // 数据
        index += data_len;
    }

    response[index++] = FRAME_TAIL;                    // 帧尾

    uart_send_data(response, index);
}

/**
 * @brief 处理连接握手/心跳命令
 */
static void handle_heartbeat(void)
{
    uint8_t status = 0x00;  // 连接成功
    send_response(RESP_HEARTBEAT, &status, 1);
}

/**
 * @brief 处理查询系统版本命令
 */
static void handle_system_version(void)
{
    send_response(RESP_SYS_VERSION, system_version, 3);
}

/**
 * @brief 处理中心配置命令
 * @param cmd_code 命令码
 * @param data 数据指针
 * @param data_len 数据长度
 */
static void handle_center_config(uint8_t cmd_code, uint8_t *data, int data_len)
{
    center_config_t *config;
    uint8_t resp_code;
    
    // 选择对应的配置结构和响应码
    switch (cmd_code) {
        case CMD_CENTER1:
            config = &center1_config;
            resp_code = RESP_CENTER1;
            break;
        case CMD_CENTER2:
            config = &center2_config;
            resp_code = RESP_CENTER2;
            break;
        case CMD_CENTER3:
            config = &center3_config;
            resp_code = RESP_CENTER3;
            break;
        default:
            return;
    }
    
    if (data_len == 0) {
        // 读取配置
        uint8_t response_data[8];
        memcpy(response_data, config->ip, 4);           // IP地址
        response_data[4] = config->port & 0xFF;         // 端口低字节
        response_data[5] = (config->port >> 8) & 0xFF;  // 端口高字节
        response_data[6] = config->period & 0xFF;       // 周期低字节
        response_data[7] = (config->period >> 8) & 0xFF; // 周期高字节
        
        send_response(resp_code, response_data, 8);
    } else if (data_len == 8) {
        // 设置配置
        memcpy(config->ip, data, 4);                    // IP地址
        config->port = data[4] | (data[5] << 8);        // 端口（小端序）
        config->period = data[6] | (data[7] << 8);      // 周期（小端序）
        
        uint8_t status = 0x00;  // 设置成功
        send_response(RESP_CONFIG_OK, &status, 1);
    }
}

/**
 * @brief 处理厂家协议配置命令
 * @param data 数据指针
 * @param data_len 数据长度
 */
static void handle_protocol_config(uint8_t *data, int data_len)
{
    if (data_len == 0) {
        // 读取厂家协议配置
        uint8_t response_data[5];
        response_data[0] = protocol_config.manufacturer;
        response_data[1] = protocol_config.baud_code;
        response_data[2] = protocol_config.data_bits;
        response_data[3] = protocol_config.stop_bits;
        response_data[4] = protocol_config.parity;
        
        send_response(RESP_PROTOCOL, response_data, 5);
    } else if (data_len == 5) {
        // 设置厂家协议配置
        protocol_config.manufacturer = data[0];
        protocol_config.baud_code = data[1];
        protocol_config.data_bits = data[2];
        protocol_config.stop_bits = data[3];
        protocol_config.parity = data[4];
        
        uint8_t status = 0x00;  // 设置成功
        send_response(RESP_CONFIG_OK, &status, 1);
    }
}

/**
 * @brief 处理其他配置命令
 * @param data 数据指针
 * @param data_len 数据长度
 */
static void handle_other_config(uint8_t *data, int data_len)
{
    if (data_len == 0) {
        // 读取其他配置
        send_response(RESP_OTHER_CONFIG, &other_config.data_compat_mode, 1);
    } else if (data_len == 1) {
        // 设置其他配置
        other_config.data_compat_mode = data[0];
        
        uint8_t status = 0x00;  // 设置成功
        send_response(RESP_CONFIG_OK, &status, 1);
    }
}

/**
 * @brief 处理终端ID配置命令
 * @param cmd_code 命令码
 * @param data 数据指针
 * @param data_len 数据长度
 */
static void handle_terminal_id(uint8_t cmd_code, uint8_t *data, int data_len)
{
    if (cmd_code == CMD_READ_TERMINAL_ID || data_len == 0) {
        // 读取终端ID
        send_response(RESP_TERMINAL_ID, terminal_id.id, 5);
    } else if (cmd_code == CMD_TERMINAL_ID && data_len == 5) {
        // 设置终端ID
        memcpy(terminal_id.id, data, 5);
        
        uint8_t status = 0x00;  // 设置成功
        send_response(RESP_CONFIG_OK, &status, 1);
    }
}

/**
 * @brief 协议数据处理主函数
 * @param length 报文长度
 * @param data 报文数据指针
 * @return 0-成功，-1-失败
 */
int protocol_process(int length, char* data)
{
    uint8_t *frame = (uint8_t*)data;

    // 基本长度检查
    if (length < 4) {
        return -1;  // 数据包太短
    }

    // 检查帧头和帧尾
    if (frame[0] != FRAME_HEAD || frame[length-1] != FRAME_TAIL) {
        return -1;  // 帧头或帧尾错误
    }

    // 解析数据包
    uint8_t frame_length = frame[1];
    uint8_t cmd_code = frame[2];
    uint8_t *payload = &frame[3];
    int payload_len = frame_length - 2;  // 减去长度字段本身和命令码字节

    // 长度校验
    if (length != frame_length + 2) {  // 总长度 = 帧头 + 长度字段内容 + 帧尾
        return -1;  // 长度不匹配
    }

    // 根据命令码处理不同的功能
    switch (cmd_code) {
        case CMD_HEARTBEAT:
            handle_heartbeat();
            break;

        case CMD_SYS_VERSION:
            handle_system_version();
            break;

        case CMD_CENTER1:
        case CMD_CENTER2:
        case CMD_CENTER3:
            handle_center_config(cmd_code, payload, payload_len);
            break;

        case CMD_PROTOCOL:
            handle_protocol_config(payload, payload_len);
            break;

        case CMD_OTHER_CONFIG:
            handle_other_config(payload, payload_len);
            break;

        case CMD_TERMINAL_ID:
        case CMD_READ_TERMINAL_ID:
            handle_terminal_id(cmd_code, payload, payload_len);
            break;
        case CMD_UPDATE:
            //置升级标志，重启
            break;

        default:
            return -1;  // 未知命令
    }

    return 0;  // 处理成功
}

// ============================================================================
// 配置数据访问接口函数（供用户应用程序调用）
// ============================================================================

/**
 * @brief 获取中心配置
 * @param center_num 中心编号（1-3）
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_center_config(int center_num, center_config_t *config)
{
    if (!config || center_num < 1 || center_num > 3) {
        return -1;
    }

    switch (center_num) {
        case 1:
            *config = center1_config;
            break;
        case 2:
            *config = center2_config;
            break;
        case 3:
            *config = center3_config;
            break;
        default:
            return -1;
    }

    return 0;
}

/**
 * @brief 设置中心配置
 * @param center_num 中心编号（1-3）
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_center_config(int center_num, const center_config_t *config)
{
    if (!config || center_num < 1 || center_num > 3) {
        return -1;
    }

    switch (center_num) {
        case 1:
            center1_config = *config;
            break;
        case 2:
            center2_config = *config;
            break;
        case 3:
            center3_config = *config;
            break;
        default:
            return -1;
    }

    return 0;
}

/**
 * @brief 获取厂家协议配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_protocol_config(protocol_config_t *config)
{
    if (!config) {
        return -1;
    }

    *config = protocol_config;
    return 0;
}

/**
 * @brief 设置厂家协议配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_protocol_config(const protocol_config_t *config)
{
    if (!config) {
        return -1;
    }

    protocol_config = *config;
    return 0;
}

/**
 * @brief 获取其他配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_other_config(other_config_t *config)
{
    if (!config) {
        return -1;
    }

    *config = other_config;
    return 0;
}

/**
 * @brief 设置其他配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_other_config(const other_config_t *config)
{
    if (!config) {
        return -1;
    }

    other_config = *config;
    return 0;
}

/**
 * @brief 获取终端ID
 * @param id 终端ID指针（5字节）
 * @return 0-成功，-1-失败
 */
int protocol_get_terminal_id(uint8_t *id)
{
    if (!id) {
        return -1;
    }

    memcpy(id, terminal_id.id, 5);
    return 0;
}

/**
 * @brief 设置终端ID
 * @param id 终端ID指针（5字节）
 * @return 0-成功，-1-失败
 */
int protocol_set_terminal_id(const uint8_t *id)
{
    if (!id) {
        return -1;
    }

    memcpy(terminal_id.id, id, 5);
    return 0;
}

/**
 * @brief 获取系统版本
 * @param version 版本字符串指针（3字节）
 * @return 0-成功，-1-失败
 */
int protocol_get_system_version(uint8_t *version)
{
    if (!version) {
        return -1;
    }

    memcpy(version, system_version, 3);
    return 0;
}

/**
 * @brief 设置系统版本
 * @param version 版本字符串指针（3字节）
 * @return 0-成功，-1-失败
 */
int protocol_set_system_version(const uint8_t *version)
{
    if (!version) {
        return -1;
    }

    memcpy(system_version, version, 3);
    return 0;
}
