/**
 * @file protocol.h
 * @brief 水表采集器配置工具通信协议 - STM32设备端头文件
 * <AUTHOR> Assistant
 * @date 2025-08-26
 */

#ifndef __PROTOCOL_H__
#define __PROTOCOL_H__

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// 协议帧格式定义
#define FRAME_HEAD          0x7E
#define FRAME_TAIL          0xCE

// 命令码定义
#define CMD_HEARTBEAT       0x02    // 连接握手/心跳
#define CMD_SYS_VERSION     0x05    // 查询系统版本
#define CMD_CENTER1         0x11    // 中心1配置
#define CMD_CENTER2         0x12    // 中心2配置  
#define CMD_CENTER3         0x13    // 中心3配置
#define CMD_PROTOCOL        0x15    // 厂家协议配置
#define CMD_OTHER_CONFIG    0x16    // 其他配置
#define CMD_TERMINAL_ID     0x17    // 设置终端ID
#define CMD_READ_TERMINAL_ID 0x18   // 读取终端ID
#define CMD_UPDATE          0x06     // 升级，不用响应

// 响应码定义
#define RESP_HEARTBEAT      0x82    // 连接确认响应
#define RESP_CONFIG_OK      0x83    // 配置设置成功响应
#define RESP_SYS_VERSION    0x85    // 系统版本响应
#define RESP_CENTER1        0x91    // 中心1响应
#define RESP_CENTER2        0x92    // 中心2响应
#define RESP_CENTER3        0x93    // 中心3响应
#define RESP_PROTOCOL       0x95    // 厂家协议响应
#define RESP_OTHER_CONFIG   0x96    // 其他配置响应
#define RESP_TERMINAL_ID    0x97    // 终端ID响应

// 厂家代码定义
#define MANUFACTURER_DALIAN     1   // 大连道盛
#define MANUFACTURER_TAIAN      2   // 泰安
#define MANUFACTURER_TANGSHAN   3   // 唐山
#define MANUFACTURER_HENAN      4   // 河南

// 波特率代码定义
#define BAUD_1200           1
#define BAUD_2400           2
#define BAUD_4800           3
#define BAUD_9600           4
#define BAUD_19200          5
#define BAUD_38400          6

// 校验位定义
#define PARITY_NONE         0   // 无校验
#define PARITY_ODD          1   // 奇校验
#define PARITY_EVEN         2   // 偶校验

// 数据兼容模式定义
#define DATA_COMPAT_OFF     0   // 关闭
#define DATA_COMPAT_ON      1   // 开启

// 配置数据结构定义
typedef struct {
    uint8_t ip[4];              // IP地址
    uint16_t port;              // 端口号（小端序）
    uint16_t period;            // 上报周期，单位分钟（小端序）
} center_config_t;

typedef struct {
    uint8_t manufacturer;       // 厂家代码
    uint8_t baud_code;         // 波特率代码
    uint8_t data_bits;         // 数据位
    uint8_t stop_bits;         // 停止位
    uint8_t parity;            // 校验位
} protocol_config_t;

typedef struct {
    uint8_t data_compat_mode;   // 数据兼容模式
} other_config_t;

typedef struct {
    uint8_t id[5];             // 终端ID，5字节
} terminal_id_t;

// 主要协议处理函数
/**
 * @brief 协议数据处理主函数
 * @param length 报文长度
 * @param data 报文数据指针
 * @return 0-成功，-1-失败
 */
int protocol_process(int length, char* data);

// 配置数据访问接口函数
/**
 * @brief 获取中心配置
 * @param center_num 中心编号（1-3）
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_center_config(int center_num, center_config_t *config);

/**
 * @brief 设置中心配置
 * @param center_num 中心编号（1-3）
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_center_config(int center_num, const center_config_t *config);

/**
 * @brief 获取厂家协议配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_protocol_config(protocol_config_t *config);

/**
 * @brief 设置厂家协议配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_protocol_config(const protocol_config_t *config);

/**
 * @brief 获取其他配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_get_other_config(other_config_t *config);

/**
 * @brief 设置其他配置
 * @param config 配置结构指针
 * @return 0-成功，-1-失败
 */
int protocol_set_other_config(const other_config_t *config);

/**
 * @brief 获取终端ID
 * @param id 终端ID指针（5字节）
 * @return 0-成功，-1-失败
 */
int protocol_get_terminal_id(uint8_t *id);

/**
 * @brief 设置终端ID
 * @param id 终端ID指针（5字节）
 * @return 0-成功，-1-失败
 */
int protocol_set_terminal_id(const uint8_t *id);

/**
 * @brief 获取系统版本
 * @param version 版本字符串指针（3字节）
 * @return 0-成功，-1-失败
 */
int protocol_get_system_version(uint8_t *version);

/**
 * @brief 设置系统版本
 * @param version 版本字符串指针（3字节）
 * @return 0-成功，-1-失败
 */
int protocol_set_system_version(const uint8_t *version);

// 外部函数声明（需要用户实现）
/**
 * @brief UART发送数据函数（用户需要实现）
 * @param data 数据指针
 * @param length 数据长度
 */
extern void uart_send_data(uint8_t *data, int length);

#ifdef __cplusplus
}
#endif

#endif /* __PROTOCOL_H__ */
