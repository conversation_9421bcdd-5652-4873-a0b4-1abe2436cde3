#include "debug.h"
#include <stdio.h>
#include "pin_definitions.h"

UART_HandleTypeDef debug_handler;

int fputc(int ch, FILE *f)
{
  HAL_UART_Transmit(&debug_handler, (uint8_t *)&ch, 1, 50);
  return ch;
}

void debug_init(uint32_t baudrate)
{
  __HAL_RCC_GPIOA_CLK_ENABLE();
  __HAL_RCC_USART4_CLK_ENABLE();

  GPIO_InitTypeDef gpio;
  gpio.Alternate = GPIO_AF6_USART4;
  gpio.Pin = DEBUG_UART_TX_PIN | DEBUG_UART_RX_PIN;
  gpio.Mode = GPIO_MODE_AF_PP;
  gpio.Pull = GPIO_NOPULL;
  gpio.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(DEBUG_UART_TX_PORT, &gpio);

  debug_handler.Instance = USART4;
  debug_handler.Init.BaudRate = baudrate;
  debug_handler.Init.Mode = UART_MODE_TX_RX;
  debug_handler.Init.Parity = UART_PARITY_NONE;
  debug_handler.Init.StopBits = UART_STOPBITS_1;
  debug_handler.Init.WordLength = UART_WORDLENGTH_8B;
  if (HAL_UART_Init(&debug_handler) != HAL_OK)
  {
    while (1);
  }

  /* Enable UART interrupt */
  __HAL_UART_ENABLE_IT(&debug_handler, UART_IT_RXNE);
//  HAL_NVIC_SetPriority(USART4_IRQn, 0, 0);
//  HAL_NVIC_EnableIRQ(USART4_IRQn);
}
