/**
 * @file uart.h
 * @brief UART串口驱动头文件
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */

#ifndef __UART_H__
#define __UART_H__

#include "system.h"
#include "user_config.h"
#include "../../common/common.h"

#define SERIAL_BUFFER_SIZE 64
#define SERIAL_TIMEOUT 30

typedef struct
{
    uint8_t rx_buffer[SERIAL_BUFFER_SIZE];
    uint8_t rx_count;
    uint8_t byte_timeout;
} SERIAL;

extern SERIAL serial_485;

// 串口接口函数
int uart_init(protocol_config_t config);
int uart_send(uint8_t *data, uint8_t len);
int uart_receive(uint8_t *data, uint8_t max_len);
uint8_t uart_available(void);
void uart_flush(void);
void uart_timeout_handler(void);
void uart_debug_status(void);
void uart_test_interrupt(void);
void uart_reset_counters(void);
void uart_loopback_test(void);

#endif
