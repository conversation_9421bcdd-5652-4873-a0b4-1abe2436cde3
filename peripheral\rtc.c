#include "rtc.h"


#define RTC_ASYNCH_PREDIV 0x7C
#define RTC_SYNCH_PREDIV 0x0127
#define RTC_BKP_DATA 0x2143

RTC_HandleTypeDef rtc_handle;

/*##-1- Configure the RTC peripheral #######################################*/
/* Configure RTC prescaler and RTC data registers */
/* RTC configured as follows:
		- Hour Format    = Format 24
		- Asynch Prediv  = Value according to source clock
		- Synch Prediv   = Value according to source clock
		- OutPut         = Output Disable
		- OutPutPolarity = High Polarity
		- OutPutType     = Open Drain */
uint8_t rtc_init(void)
{
	__HAL_RCC_RTC_ENABLE();

	RCC_OscInitTypeDef rcc_osc;
	RCC_PeriphCLKInitTypeDef rcc_clk;
  
	
// 禁用RTC输出到PC13
 // hrtc.Init.OutPut = RTC_OUTPUT_DISABLE;  // 关键配置：禁用RTC输出
	/*##-1- Enables the PWR Clock and Enables access to the backup domain ###################################*/
	/* To change the source clock of the RTC feature (LSE, LSI), You have to:
		 - Enable the power clock using __HAL_RCC_PWR_CLK_ENABLE()
		 - Enable write access using HAL_PWR_EnableBkUpAccess() function before to
			 configure the RTC clock source (to be done once after reset).
		 - Reset the Back up Domain using __HAL_RCC_BACKUPRESET_FORCE() and
			 __HAL_RCC_BACKUPRESET_RELEASE().
		 - Configure the needed RTC clock source */
	__HAL_RCC_PWR_CLK_ENABLE();
	HAL_PWR_EnableBkUpAccess();

	rcc_osc.OscillatorType = RCC_OSCILLATORTYPE_LSI | RCC_OSCILLATORTYPE_LSE;
	rcc_osc.LSIState = RCC_LSI_ON;
	rcc_osc.LSEState = RCC_LSE_OFF;
	rcc_osc.PLL.PLLState = RCC_PLL_NONE;
	if(HAL_RCC_OscConfig(&rcc_osc) != HAL_OK)
	 return 0;
	 rcc_clk.PeriphClockSelection = RCC_PERIPHCLK_RTC;
	 rcc_clk.RTCClockSelection = RCC_RTCCLKSOURCE_LSI;
	if(HAL_RCCEx_PeriphCLKConfig(&rcc_clk) != HAL_OK)
		return 0;

	rtc_handle.Instance = RTC;
	rtc_handle.Init.AsynchPrediv = RTC_ASYNCH_PREDIV;
	rtc_handle.Init.HourFormat = RTC_HOURFORMAT_24;
	rtc_handle.Init.OutPut = RTC_OUTPUT_DISABLE;
	rtc_handle.Init.OutPutPolarity = RTC_OUTPUT_POLARITY_HIGH;
	rtc_handle.Init.OutPutType = RTC_OUTPUT_TYPE_OPENDRAIN;
	rtc_handle.Init.SynchPrediv = RTC_SYNCH_PREDIV;

	if (HAL_RTC_Init(&rtc_handle) != HAL_OK)
		return 0;
	

	if(HAL_RTCEx_BKUPRead(&rtc_handle, RTC_BKP_DR1) != RTC_BKP_DATA)
	{
		rtc_set_time(2025,8,1,12,00,00);
		HAL_RTCEx_BKUPWrite(&rtc_handle, RTC_BKP_DR1, RTC_BKP_DATA);
	}
	return 1;
}

void rtc_set_time(uint16_t year, uint8_t month, uint8_t day, uint8_t hour, uint8_t minute, uint8_t second)
{
	RTC_DateTypeDef date; //  日期结构体变量，用于存储年、月、日等信息
	RTC_TimeTypeDef time; //  时间结构体变量，用于存储时、分、秒等信息

	date.Year = year - 2000; //  设置年份，减去2000是因为RTC通常以2000年为基准
	date.Month = month; //  设置月份
	date.Date = day; //  设置日期	
	date.WeekDay = RTC_WEEKDAY_MONDAY; //  设置星期		

	time.Hours = hour; //  设置小时
	time.Minutes = minute;	//  设置分钟
	time.Seconds = second;		//  设置秒
	time.TimeFormat = RTC_HOURFORMAT_24; //  设置时间格式为24小时制
	

	HAL_RTC_SetDate(&rtc_handle, &date, RTC_FORMAT_BIN); //  设置日期，使用二进制格式
	HAL_RTC_SetTime(&rtc_handle, &time, RTC_FORMAT_BIN); //  设置时间，使用二进制格式
}

void rtc_get_calendar(uint16_t *year, uint8_t *month, uint8_t *day,uint8_t *hour, uint8_t *minute, uint8_t *second)
{
	RTC_DateTypeDef date; //  日期结构体变量，用于存储年、月、日等信息


	HAL_RTC_GetDate(&rtc_handle, &date, RTC_FORMAT_BIN);	//  获取日期，使用二进制格式

	if(year != NULL)*year = date.Year + 2000;
	if(month != NULL)*month = date.Month;	//  获取月份
	if(day != NULL)*day = date.Date;	//  获取日期

	RTC_TimeTypeDef time;
	HAL_RTC_GetTime(&rtc_handle, &time, RTC_FORMAT_BIN);	//  获取时间，使用二进制格式

	if(hour != NULL)*hour = time.Hours;			//  获取小时

	if(minute != NULL)*minute = time.Minutes;	//  获取分钟

	if(second != NULL)*second = time.Seconds;		//  获取秒
}

uint8_t rtc_set_alarm(uint8_t hour, uint8_t minute, uint8_t second)
{
	RTC_AlarmTypeDef alarm;
	alarm.AlarmTime.Hours = hour;
	alarm.AlarmTime.Minutes = minute;
	alarm.AlarmTime.Seconds = second;
	alarm.AlarmTime.TimeFormat = RTC_HOURFORMAT_24;
	alarm.AlarmDateWeekDay = RTC_WEEKDAY_MONDAY;
	alarm.Alarm = RTC_ALARM_A;
	alarm.AlarmDateWeekDaySel = RTC_ALARMDATEWEEKDAYSEL_DATE;
	alarm.AlarmMask = RTC_ALARMMASK_DATEWEEKDAY;
	alarm.AlarmSubSecondMask = RTC_ALARMSUBSECONDMASK_NONE;
	if(HAL_RTC_SetAlarm_IT(&rtc_handle, &alarm, RTC_FORMAT_BIN) != HAL_OK)
		return 0;
	return 1;
}
