/**
 * @file uart.c
 * @brief UART串口驱动实现
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */

#include "uart.h"
#include <stdio.h>
#include <string.h>
#include "../app/include/pin_definitions.h"
#include "common.h"

// 全局变量
SERIAL serial_485;
UART_HandleTypeDef serial_485_handler;

// 调试计数器
volatile uint32_t uart_irq_count = 0;
volatile uint32_t uart_rxne_count = 0;
volatile uint32_t uart_error_count = 0;

/**
 * @brief 根据波特率代码获取实际波特率值
 * @param baud_code 波特率代码
 * @return 实际波特率值
 */
static uint32_t get_baud_rate(uint8_t baud_code)
{
    switch(baud_code)
    {
        case BAUD_1200:  return 1200;
        case BAUD_2400:  return 2400;
        case BAUD_4800:  return 4800;
        case BAUD_9600:  return 9600;
        case BAUD_19200: return 19200;
        case BAUD_38400: return 38400;
        default:         return 9600;
    }
}

/**
 * @brief 根据校验位代码获取HAL校验位配置
 * @param parity 校验位代码
 * @return HAL校验位配置
 */
static uint32_t get_parity(uint8_t parity)
{
    switch(parity)
    {
        case PARITY_NONE: return UART_PARITY_NONE;
        case PARITY_ODD:  return UART_PARITY_ODD;
        case PARITY_EVEN: return UART_PARITY_EVEN;
        default:          return UART_PARITY_NONE;
    }
}

/**
 * @brief 根据数据位获取HAL字长配置
 * @param data_bits 数据位数
 * @return HAL字长配置
 */
static uint32_t get_word_length(uint8_t data_bits)
{
    switch(data_bits)
    {
        case 7:  return UART_WORDLENGTH_7B;
        case 8:  return UART_WORDLENGTH_8B;
        case 9:  return UART_WORDLENGTH_9B;
        default: return UART_WORDLENGTH_8B;
    }
}

/**
 * @brief 根据停止位获取HAL停止位配置
 * @param stop_bits 停止位数
 * @return HAL停止位配置
 */
static uint32_t get_stop_bits(uint8_t stop_bits)
{
    switch(stop_bits)
    {
        case 1:  return UART_STOPBITS_1;
        case 2:  return UART_STOPBITS_2;
        default: return UART_STOPBITS_1;
    }
}

/**
 * @brief 串口初始化
 * @param config 协议配置参数
 * @return 0-成功，其他-失败
 */
int uart_init(protocol_config_t config)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 使能时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_USART2_CLK_ENABLE();

    // 配置RS485_TX_EN (PB8) 为输出引脚
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pin = RS485_TX_EN_PIN;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_MEDIUM;
    HAL_GPIO_Init(RS485_TX_EN_PORT, &GPIO_InitStruct);
    RS485_TX_EN(0); // 默认为接收模式

    // 配置RS485_PWR (PA11) 为输出引脚
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pin = RS485_PWR_PIN;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_MEDIUM;
    HAL_GPIO_Init(RS485_PWR_PORT, &GPIO_InitStruct);
    RS485_PWR(1); // 默认上电

    // 配置USART2 TX引脚 (RS485_TX PA2)
    GPIO_InitStruct.Pin = RS485_TX_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF4_USART2;
    HAL_GPIO_Init(RS485_TX_PORT, &GPIO_InitStruct);

    // 配置USART2 RX引脚 (RS485_RX PA3)
    GPIO_InitStruct.Pin = RS485_RX_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;  // RX引脚使用上拉
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF4_USART2;
    HAL_GPIO_Init(RS485_RX_PORT, &GPIO_InitStruct);

    // 清空接收缓冲区
    memset(&serial_485, 0, sizeof(serial_485));

    // 配置UART参数
    serial_485_handler.Instance = USART2;
    serial_485_handler.Init.BaudRate = get_baud_rate(config.baud_code);
    serial_485_handler.Init.WordLength = get_word_length(config.data_bits);
    serial_485_handler.Init.StopBits = get_stop_bits(config.stop_bits);
    serial_485_handler.Init.Parity = get_parity(config.parity);
    serial_485_handler.Init.Mode = UART_MODE_TX_RX;
    serial_485_handler.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    serial_485_handler.Init.OverSampling = UART_OVERSAMPLING_16;

    // 初始化UART
    if(HAL_UART_Init(&serial_485_handler) != HAL_OK)
    {
        return -1; // 初始化失败
    }

    // 设置中断优先级并使能中断
    HAL_NVIC_SetPriority(USART2_IRQn, 3, 3);
    HAL_NVIC_EnableIRQ(USART2_IRQn);

    // 使能UART接收中断
    __HAL_UART_ENABLE_IT(&serial_485_handler, UART_IT_RXNE);

    // 确保中断标志被清除
    __HAL_UART_CLEAR_FLAG(&serial_485_handler, UART_CLEAR_OREF);
    __HAL_UART_CLEAR_FLAG(&serial_485_handler, UART_CLEAR_FEF);
    __HAL_UART_CLEAR_FLAG(&serial_485_handler, UART_CLEAR_PEF);

    return 0; // 初始化成功
}

/**
 * @brief 串口发送数据
 * @param data 要发送的数据指针
 * @param len 数据长度
 * @return 0-成功，其他-失败
 */
int uart_send(uint8_t *data, uint8_t len)
{
    if(data == NULL || len == 0)
    {
        return -1; // 参数错误
    }

    RS485_TX_EN(1);  // 切换到发送模式

    // 发送数据
    for(uint8_t i = 0; i < len; i++)
    {
        if(HAL_UART_Transmit(&serial_485_handler, &data[i], 1, 50) != HAL_OK)
        {
            RS485_TX_EN(0);  // 发送失败，切换回接收模式
            return -1;
        }
    }

    // 等待发送完成
    while(__HAL_UART_GET_FLAG(&serial_485_handler, UART_FLAG_TC) == RESET);

    // 添加小延时确保数据完全发送
    HAL_Delay(1);

    RS485_TX_EN(0);  // 切换回接收模式

    // 再添加小延时确保RS485切换到接收模式
    HAL_Delay(1);

    return 0; // 发送成功
}

/**
 * @brief 串口接收数据
 * @param data 接收数据缓冲区指针
 * @param max_len 缓冲区最大长度
 * @return 实际接收到的数据长度，-1表示无数据
 */
int uart_receive(uint8_t *data, uint8_t max_len)
{
    if(data == NULL || max_len == 0)
    {
        return -1; // 参数错误
    }

    // 检查是否有数据
    if(serial_485.rx_count == 0)
    {
        return -1; // 无数据
    }

    // 复制数据
    uint8_t copy_len = (serial_485.rx_count > max_len) ? max_len : serial_485.rx_count;
    memcpy(data, serial_485.rx_buffer, copy_len);

    // 清空接收缓冲区
    serial_485.rx_count = 0;
    serial_485.byte_timeout = 0;

    return copy_len; // 返回实际接收长度
}

/**
 * @brief 检查是否有数据可读
 * @return 可读数据长度，0表示无数据
 */
uint8_t uart_available(void)
{
    return serial_485.rx_count;
}

/**
 * @brief 清空接收缓冲区
 */
void uart_flush(void)
{
    serial_485.rx_count = 0;
    serial_485.byte_timeout = 0;
    memset(serial_485.rx_buffer, 0, SERIAL_BUFFER_SIZE);
}

/**
 * @brief USART2中断处理函数
 */
void USART2_IRQHandler(void)
{
    volatile uint32_t isr_reg = serial_485_handler.Instance->ISR;

    // 增加中断计数器
    uart_irq_count++;

    // 添加调试信息（可以临时启用）
    // printf("IRQ: ISR=0x%08X\n", isr_reg);

    // 检查溢出错误标志 (ORE bit 3) - 必须先处理
    if(isr_reg & UART_FLAG_ORE)
    {
        uart_error_count++;
        // 清除溢出错误：读取数据寄存器
        (void)serial_485_handler.Instance->RDR;
        // 清除错误标志
        __HAL_UART_CLEAR_FLAG(&serial_485_handler, UART_CLEAR_OREF);
        printf("UART溢出错误已清除\n");
    }

    // 检查帧错误标志 (FE bit 1)
    if(isr_reg & UART_FLAG_FE)
    {
        uart_error_count++;
        // 清除帧错误
        __HAL_UART_CLEAR_FLAG(&serial_485_handler, UART_CLEAR_FEF);
        printf("UART帧错误已清除\n");
    }

    // 检查校验错误标志 (PE bit 0)
    if(isr_reg & UART_FLAG_PE)
    {
        uart_error_count++;
        // 清除校验错误
        __HAL_UART_CLEAR_FLAG(&serial_485_handler, UART_CLEAR_PEF);
        printf("UART校验错误已清除\n");
    }

    // 检查接收中断标志 (RXNE bit 5)
    if(isr_reg & UART_FLAG_RXNE)
    {
        uart_rxne_count++;

        // 读取数据寄存器
        uint8_t received_data = (uint8_t)(serial_485_handler.Instance->RDR);

        // 存储到缓冲区
        if(serial_485.rx_count < SERIAL_BUFFER_SIZE)
        {
            serial_485.rx_buffer[serial_485.rx_count++] = received_data;
        }
        else
        {
            // 缓冲区满，重置
            serial_485.rx_count = 0;
            serial_485.rx_buffer[serial_485.rx_count++] = received_data;
        }

        // 设置超时
        serial_485.byte_timeout = SERIAL_TIMEOUT;

        // 添加调试信息（可以临时启用）
        // printf("RX: 0x%02X, count=%d\n", received_data, serial_485.rx_count);
    }
}

/**
 * @brief 串口超时处理（需要在主循环中定期调用）
 */
void uart_timeout_handler(void)
{
    if(serial_485.byte_timeout > 0)
    {
        serial_485.byte_timeout--;
        if(serial_485.byte_timeout == 0)
        {
            // 超时处理，可以在这里添加超时后的处理逻辑
            // 例如：设置接收完成标志等
        }
    }
}

/**
 * @brief 获取串口状态信息（调试用）
 */
void uart_debug_status(void)
{
    uint32_t isr = serial_485_handler.Instance->ISR;
    uint32_t cr1 = serial_485_handler.Instance->CR1;

    printf("串口状态调试信息:\n");
    printf("  接收缓冲区数据量: %d\n", serial_485.rx_count);
    printf("  超时计数器: %d\n", serial_485.byte_timeout);
    printf("  UART状态寄存器: 0x%08X\n", isr);
    printf("    RXNE(接收非空): %d\n", (isr & UART_FLAG_RXNE) ? 1 : 0);
    printf("    TC(发送完成): %d\n", (isr & UART_FLAG_TC) ? 1 : 0);
    printf("    TXE(发送空): %d\n", (isr & UART_FLAG_TXE) ? 1 : 0);
    printf("    ORE(溢出错误): %d\n", (isr & UART_FLAG_ORE) ? 1 : 0);
    printf("    FE(帧错误): %d\n", (isr & UART_FLAG_FE) ? 1 : 0);
    printf("    PE(校验错误): %d\n", (isr & UART_FLAG_PE) ? 1 : 0);
    printf("  UART控制寄存器1: 0x%08X\n", cr1);
    printf("    RXNEIE(接收中断使能): %d\n", (cr1 & UART_IT_RXNE) ? 1 : 0);
    printf("    UE(UART使能): %d\n", (cr1 & USART_CR1_UE) ? 1 : 0);
    printf("  RS485_TX_EN引脚状态: %d\n", HAL_GPIO_ReadPin(RS485_TX_EN_PORT, RS485_TX_EN_PIN));
    printf("  NVIC中断使能状态: %d\n", NVIC_GetEnableIRQ(USART2_IRQn));
    printf("  USART2时钟使能状态: %d\n", __HAL_RCC_USART2_IS_CLK_ENABLED());
    printf("  GPIOA时钟使能状态: %d\n", __HAL_RCC_GPIOA_IS_CLK_ENABLED());
    printf("  GPIOB时钟使能状态: %d\n", __HAL_RCC_GPIOB_IS_CLK_ENABLED());
    printf("调试计数器:\n");
    printf("  总中断次数: %lu\n", uart_irq_count);
    printf("  RXNE中断次数: %lu\n", uart_rxne_count);
    printf("  错误中断次数: %lu\n", uart_error_count);
}

/**
 * @brief 重置调试计数器
 */
void uart_reset_counters(void)
{
    uart_irq_count = 0;
    uart_rxne_count = 0;
    uart_error_count = 0;
    printf("UART调试计数器已重置\n");
}

/**
 * @brief 强制触发接收中断测试（调试用）
 */
void uart_test_interrupt(void)
{
    printf("测试UART中断处理...\n");

    // 模拟接收到一个字节
    if(serial_485.rx_count < SERIAL_BUFFER_SIZE) {
        serial_485.rx_buffer[serial_485.rx_count++] = 0xAA;
        printf("手动添加测试数据0xAA到缓冲区\n");
        printf("缓冲区数据量: %d\n", serial_485.rx_count);
    }
}

/**
 * @brief UART回环测试（发送数据并检查是否能接收到）
 */
void uart_loopback_test(void)
{
    printf("开始UART回环测试...\n");

    // 重置计数器
    uart_reset_counters();

    // 发送一个测试字节
    uint8_t test_byte = 0x55;
    printf("发送测试字节: 0x%02X\n", test_byte);

    // 临时连接TX和RX（软件模拟，实际需要硬件连接）
    // 这里我们直接模拟接收到数据来测试中断

    // 检查发送前的状态
    printf("发送前中断计数: %lu\n", uart_irq_count);

    // 发送数据
    uart_send(&test_byte, 1);

    // 等待一段时间
    HAL_Delay(100);

    // 检查发送后的状态
    printf("发送后中断计数: %lu\n", uart_irq_count);
    printf("接收到的数据量: %d\n", uart_available());

    // 手动触发中断来测试中断处理函数
    printf("手动触发USART2中断...\n");
    NVIC_SetPendingIRQ(USART2_IRQn);

    // 等待中断处理
    HAL_Delay(10);

    printf("手动触发后中断计数: %lu\n", uart_irq_count);
}


