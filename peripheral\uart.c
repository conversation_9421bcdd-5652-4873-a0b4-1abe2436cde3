/**
 * @file uart.c
 * @brief UART串口驱动实现
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */

#include "uart.h"
#include <stdio.h>
#include <string.h>
#include "pin_definitions.h"
#include "common.h"

// 全局变量
SERIAL serial_485;
UART_HandleTypeDef serial_485_handler;

/**
 * @brief 根据波特率代码获取实际波特率值
 * @param baud_code 波特率代码
 * @return 实际波特率值
 */
static uint32_t get_baud_rate(uint8_t baud_code)
{
    switch(baud_code)
    {
        case BAUD_1200:  return 1200;
        case BAUD_2400:  return 2400;
        case BAUD_4800:  return 4800;
        case BAUD_9600:  return 9600;
        case BAUD_19200: return 19200;
        case BAUD_38400: return 38400;
        default:         return 9600;
    }
}

/**
 * @brief 根据校验位代码获取HAL校验位配置
 * @param parity 校验位代码
 * @return HAL校验位配置
 */
static uint32_t get_parity(uint8_t parity)
{
    switch(parity)
    {
        case PARITY_NONE: return UART_PARITY_NONE;
        case PARITY_ODD:  return UART_PARITY_ODD;
        case PARITY_EVEN: return UART_PARITY_EVEN;
        default:          return UART_PARITY_NONE;
    }
}

/**
 * @brief 根据数据位获取HAL字长配置
 * @param data_bits 数据位数
 * @return HAL字长配置
 */
static uint32_t get_word_length(uint8_t data_bits)
{
    switch(data_bits)
    {
        case 7:  return UART_WORDLENGTH_7B;
        case 8:  return UART_WORDLENGTH_8B;
        case 9:  return UART_WORDLENGTH_9B;
        default: return UART_WORDLENGTH_8B;
    }
}

/**
 * @brief 根据停止位获取HAL停止位配置
 * @param stop_bits 停止位数
 * @return HAL停止位配置
 */
static uint32_t get_stop_bits(uint8_t stop_bits)
{
    switch(stop_bits)
    {
        case 1:  return UART_STOPBITS_1;
        case 2:  return UART_STOPBITS_2;
        default: return UART_STOPBITS_1;
    }
}

/**
 * @brief 串口初始化
 * @param config 协议配置参数
 * @return 0-成功，其他-失败
 */
int uart_init(protocol_config_t config)
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    // 使能时钟
    __HAL_RCC_GPIOA_CLK_ENABLE();
    __HAL_RCC_GPIOB_CLK_ENABLE();
    __HAL_RCC_USART2_CLK_ENABLE();

    // 配置RS485_TX_EN (PB8) 为输出引脚
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pin = RS485_TX_EN_PIN;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_MEDIUM;
    HAL_GPIO_Init(RS485_TX_EN_PORT, &GPIO_InitStruct);
    RS485_TX_EN(0); // 默认为接收模式

    // 配置RS485_PWR (PA11) 为输出引脚
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pin = RS485_PWR_PIN;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_MEDIUM;
    HAL_GPIO_Init(RS485_PWR_PORT, &GPIO_InitStruct);
    RS485_PWR(1); // 默认上电

    // 配置USART2 TX/RX引脚 (RS485_TX PA2, RS485_RX PA3)
    GPIO_InitStruct.Pin = RS485_TX_PIN | RS485_RX_PIN;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF4_USART2;
    HAL_GPIO_Init(RS485_TX_PORT, &GPIO_InitStruct);

    // 配置UART参数
    serial_485_handler.Instance = USART2;
    serial_485_handler.Init.BaudRate = get_baud_rate(config.baud_code);
    serial_485_handler.Init.WordLength = get_word_length(config.data_bits);
    serial_485_handler.Init.StopBits = get_stop_bits(config.stop_bits);
    serial_485_handler.Init.Parity = get_parity(config.parity);
    serial_485_handler.Init.Mode = UART_MODE_TX_RX;
    serial_485_handler.Init.HwFlowCtl = UART_HWCONTROL_NONE;
    serial_485_handler.Init.OverSampling = UART_OVERSAMPLING_16;

    // 设置中断优先级
    HAL_NVIC_SetPriority(USART2_IRQn, 3, 3);
    HAL_NVIC_EnableIRQ(USART2_IRQn);

    // 初始化UART
    if(HAL_UART_Init(&serial_485_handler) != HAL_OK)
    {
        return -1; // 初始化失败
    }

    // 清空接收缓冲区
    memset(&serial_485, 0, sizeof(serial_485));

    // 使能UART接收中断
    __HAL_UART_ENABLE_IT(&serial_485_handler, UART_IT_RXNE);

    return 0; // 初始化成功
}

/**
 * @brief 串口发送数据
 * @param data 要发送的数据指针
 * @param len 数据长度
 * @return 0-成功，其他-失败
 */
int uart_send(uint8_t *data, uint8_t len)
{
    if(data == NULL || len == 0)
    {
        return -1; // 参数错误
    }

    RS485_TX_EN(1);  // 切换到发送模式

    // 发送数据
    for(uint8_t i = 0; i < len; i++)
    {
        if(HAL_UART_Transmit(&serial_485_handler, &data[i], 1, 50) != HAL_OK)
        {
            RS485_TX_EN(0);  // 发送失败，切换回接收模式
            return -1;
        }
    }

    // 等待发送完成
    while(__HAL_UART_GET_FLAG(&serial_485_handler, UART_FLAG_TC) == RESET);

    RS485_TX_EN(0);  // 切换回接收模式

    return 0; // 发送成功
}

/**
 * @brief 串口接收数据
 * @param data 接收数据缓冲区指针
 * @param max_len 缓冲区最大长度
 * @return 实际接收到的数据长度，-1表示无数据
 */
int uart_receive(uint8_t *data, uint8_t max_len)
{
    if(data == NULL || max_len == 0)
    {
        return -1; // 参数错误
    }

    // 检查是否有数据
    if(serial_485.rx_count == 0)
    {
        return -1; // 无数据
    }

    // 复制数据
    uint8_t copy_len = (serial_485.rx_count > max_len) ? max_len : serial_485.rx_count;
    memcpy(data, serial_485.rx_buffer, copy_len);

    // 清空接收缓冲区
    serial_485.rx_count = 0;
    serial_485.byte_timeout = 0;

    return copy_len; // 返回实际接收长度
}

/**
 * @brief 检查是否有数据可读
 * @return 可读数据长度，0表示无数据
 */
uint8_t uart_available(void)
{
    return serial_485.rx_count;
}

/**
 * @brief 清空接收缓冲区
 */
void uart_flush(void)
{
    serial_485.rx_count = 0;
    serial_485.byte_timeout = 0;
    memset(serial_485.rx_buffer, 0, SERIAL_BUFFER_SIZE);
}

/**
 * @brief USART2中断处理函数
 */
void USART2_IRQHandler(void)
{
    volatile uint32_t isr_reg = serial_485_handler.Instance->ISR;

    // 检查接收中断标志 (RXNE bit 5)
    if(isr_reg & (1 << 5))
    {
        // 读取数据寄存器
        uint8_t received_data = (uint8_t)(serial_485_handler.Instance->RDR);

        // 存储到缓冲区
        if(serial_485.rx_count < SERIAL_BUFFER_SIZE)
        {
            serial_485.rx_buffer[serial_485.rx_count++] = received_data;
        }
        else
        {
            // 缓冲区满，重置
            serial_485.rx_count = 0;
            serial_485.rx_buffer[serial_485.rx_count++] = received_data;
        }

        // 设置超时
        serial_485.byte_timeout = SERIAL_TIMEOUT;
    }

    // 清除可能的错误标志 (ORE bit 3, FE bit 1, PE bit 0)
    if(isr_reg & ((1 << 3) | (1 << 1) | (1 << 0)))
    {
        // 读取数据寄存器清除错误
        (void)serial_485_handler.Instance->RDR;
    }
}

/**
 * @brief 串口超时处理（需要在主循环中定期调用）
 */
void uart_timeout_handler(void)
{
    if(serial_485.byte_timeout > 0)
    {
        serial_485.byte_timeout--;
        if(serial_485.byte_timeout == 0)
        {
            // 超时处理，可以在这里添加超时后的处理逻辑
            // 例如：设置接收完成标志等
        }
    }
}

/**
 * @brief 兼容旧接口：串口发送数据
 * @param dat 要发送的数据指针
 * @param len 数据长度
 */
void serial_485_send_dat(uint8_t *dat, uint8_t len)
{
    uart_send(dat, len);
}

/**
 * @brief 兼容旧接口：串口初始化
 * @param info 水表信息结构体
 */
void serial_485_init(WATER_METER_INFO info)
{
    protocol_config_t config;

    // 转换旧的配置结构到新的配置结构
    config.manufacturer = (uint8_t)info.protocol_type + 1; // 协议类型转换为厂家代码

    // 波特率代码转换
    switch(info.baud_rate_id)
    {
        case BAUD_RATE_ID_1200:  config.baud_code = BAUD_1200; break;
        case BAUD_RATE_ID_2400:  config.baud_code = BAUD_2400; break;
        case BAUD_RATE_ID_4800:  config.baud_code = BAUD_4800; break;
        case BAUD_RATE_ID_9600:  config.baud_code = BAUD_9600; break;
        case BAUD_RATE_ID_19200: config.baud_code = BAUD_19200; break;
        case BAUD_RATE_ID_38400: config.baud_code = BAUD_38400; break;
        default:                 config.baud_code = BAUD_9600; break;
    }

    config.data_bits = (uint8_t)info.data_bit_id;
    config.stop_bits = (uint8_t)info.stop_bit_id;

    // 校验位转换
    switch(info.parity_id)
    {
        case PARITY_ID_NONE: config.parity = PARITY_NONE; break;
        case PARITY_ID_ODD:  config.parity = PARITY_ODD; break;
        case PARITY_ID_EVEN: config.parity = PARITY_EVEN; break;
        default:             config.parity = PARITY_NONE; break;
    }

    // 调用新的初始化函数
    uart_init(config);
}
