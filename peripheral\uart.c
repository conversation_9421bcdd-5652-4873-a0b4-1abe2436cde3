#include "uart.h"
#include <stdio.h>

#include <string.h>
#include "pin_definitions.h"

SERIAL serial_485;
UART_HandleTypeDef serial_485_handler;

void serial_485_send_dat(uint8_t *dat,uint8_t len)
{
  RS485_TX_EN(1);  // 使用新的引脚定义宏
	for(uint8_t i = 0;i < len;i++)
		HAL_UART_Transmit(&serial_485_handler,&dat[i],1,50);

  RS485_TX_EN(0);  // 使用新的引脚定义宏
}

void serial_485_init(WATER_METER_INFO info)
{
  __HAL_RCC_GPIOA_CLK_ENABLE();
	__HAL_RCC_GPIOB_CLK_ENABLE();
  __HAL_RCC_USART2_CLK_ENABLE();	///////

 GPIO_InitTypeDef GPIO_InitStruct = {0};

  // 配置RS485_TX_EN (PB8) 为输出引脚
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pin = RS485_TX_EN_PIN;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_MEDIUM;
  HAL_GPIO_Init(RS485_TX_EN_PORT,&GPIO_InitStruct);
  RS485_TX_EN(0); // 默认为接收模式

  // 配置RS485_PWR (PA11) 为输出引脚
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pin = RS485_PWR_PIN;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_MEDIUM;
  HAL_GPIO_Init(RS485_PWR_PORT,&GPIO_InitStruct);
  RS485_PWR(1); // 默认上电

  

  // 配置USART2 TX/RX引脚 (RS485_TX PA2, RS485_RX PA3)
  GPIO_InitStruct.Pin = RS485_TX_PIN | RS485_RX_PIN;
  GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
  GPIO_InitStruct.Alternate = GPIO_AF4_USART2;
  HAL_GPIO_Init(RS485_TX_PORT, &GPIO_InitStruct);

  serial_485_handler.Instance = USART2;
  switch(info.baud_rate_id)
  {
    case BAUD_RATE_ID_1200: serial_485_handler.Init.BaudRate = 1200;break;
    case BAUD_RATE_ID_2400: serial_485_handler.Init.BaudRate = 2400;break;
    case BAUD_RATE_ID_4800: serial_485_handler.Init.BaudRate = 4800;break;
    case BAUD_RATE_ID_9600: serial_485_handler.Init.BaudRate = 9600;break;
    case BAUD_RATE_ID_19200: serial_485_handler.Init.BaudRate = 19200;break;
    case BAUD_RATE_ID_38400: serial_485_handler.Init.BaudRate =38400;break;
    default: serial_485_handler.Init.BaudRate = 9600;break;
	} 
	
  switch(info.parity_id)
  {
    case PARITY_ID_NONE: serial_485_handler.Init.Parity = UART_PARITY_NONE;break;
    case PARITY_ID_ODD: serial_485_handler.Init.Parity = UART_PARITY_ODD;break;
    case PARITY_ID_EVEN: serial_485_handler.Init.Parity = UART_PARITY_EVEN;break;
    default: serial_485_handler.Init.Parity = UART_PARITY_NONE;break;
  }
  switch(info.stop_bit_id)
  {
    case STOP_BIT_ID_1: serial_485_handler.Init.StopBits = UART_STOPBITS_1;break;
    case STOP_BIT_ID_2: serial_485_handler.Init.StopBits = UART_STOPBITS_2;break;
    default: serial_485_handler.Init.StopBits = UART_STOPBITS_1;break;
  }
  switch(info.data_bit_id)
  {
    case DATA_BIT_ID_7: serial_485_handler.Init.WordLength = UART_WORDLENGTH_7B;break;
    case DATA_BIT_ID_8: serial_485_handler.Init.WordLength = UART_WORDLENGTH_8B;break;
    case DATA_BIT_ID_9: serial_485_handler.Init.WordLength = UART_WORDLENGTH_9B;break;
    default: serial_485_handler.Init.WordLength = UART_WORDLENGTH_8B;break;
  }
  serial_485_handler.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  serial_485_handler.Init.Mode = UART_MODE_TX_RX;

  // 先设置中断优先级，再初始化UART
  HAL_NVIC_SetPriority(USART2_IRQn, 3, 3);
  HAL_NVIC_EnableIRQ(USART2_IRQn);

  // 初始化UART
  if(HAL_UART_Init(&serial_485_handler) != HAL_OK)
  {
    // UART初始化失败处理
    return;
  }

  // 最后使能UART接收中断
  __HAL_UART_ENABLE_IT(&serial_485_handler, UART_IT_RXNE);
}

void USART2_IRQHandler(void)
{
  volatile uint32_t isr_reg = serial_485_handler.Instance->ISR;

  // 检查接收中断标志 (RXNE bit 5)
  if(isr_reg & (1 << 5))
  {
    // 读取数据寄存器
    uint8_t received_data = (uint8_t)(serial_485_handler.Instance->RDR);

    // 存储到缓冲区
    if(serial_485.rx_count < SERIAL_BUFFER_SIZE)
    {
      serial_485.rx_buffer[serial_485.rx_count++] = received_data;
    }
    else
    {
      // 缓冲区满，重置
      serial_485.rx_count = 0;
      serial_485.rx_buffer[serial_485.rx_count++] = received_data;
    }

    // 设置超时
    serial_485.byte_timeout = SERIAL_TIMEOUT;
  }

  // 清除可能的错误标志 (ORE bit 3, FE bit 1, PE bit 0)
  if(isr_reg & ((1 << 3) | (1 << 1) | (1 << 0)))
  {
    // 读取数据寄存器清除错误
    (void)serial_485_handler.Instance->RDR;
  }
}
