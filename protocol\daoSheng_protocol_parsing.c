#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "modbus_rtu.h"
#include "common/common.h"
#include "../peripheral/include/uart.h"  // 包含新的串口驱动头文件
#include "stm32l0xx_hal.h"  // 包含HAL库头文件

// 串口数据结构定义
typedef struct {
    uint8_t rx_buffer[256];  // 接收缓冲区
    uint8_t rx_count;        // 接收数据长度
} SERIAL;

/**
 * @brief 清零所有水表数据
 * @note 在读取失败时调用，将所有全局变量重置为0
 */
void clearWaterMeterData(void)
{
    g_posFlow = 0;              // 正累计流量清零
    g_negFlow = 0;              // 负累计流量清零
    g_netFlow = 0;              // 净累计流量清零
    g_instFlow = 0.0f;          // 瞬时流量清零
    g_velocity = 0.0f;          // 流速清零
    g_batteryVoltage = 0.0f;    // 水表电池电压清零
    memset(g_esnStr, 0, sizeof(g_esnStr));  // ESN字符串清零
    g_water_meter_read_flag = 0;  // 成功标志清零
    printf("调试信息：所有水表数据已清零\n");
}

/**
 * @brief 发送读取水表数据的固定报文
 * @note 发送Modbus RTU命令：01 03 05 A1 00 1C 15 2D
 *       地址：01，功能码：03（读保持寄存器），起始地址：05A1，寄存器数量：001C
 */
void sendWaterMeterReadCommand(void)
{
    uint8_t readCommand[] = {0x01, 0x03, 0x05, 0xA1, 0x00, 0x1C, 0x15, 0x2D};

    // 打印发送的命令（调试用）
    printf("发送读取命令: ");
    for (int i = 0; i < sizeof(readCommand); i++) {
        printf("%02X ", readCommand[i]);
    }
    printf("\n");

    // 使用新的串口发送函数
    if(uart_send(readCommand, sizeof(readCommand)) == 0) {
        printf("命令发送成功\n");
    } else {
        printf("命令发送失败\n");
    }
}

/**
 * @brief 解析水表数据
 * @param buf 接收到的数据缓冲区
 * @param len 数据长度
 * @return 1-解析成功，0-解析失败
 * @note 解析Modbus RTU响应数据，提取水表各项参数并存储到全局变量中
 */
int parseWaterMeterData(const uint8_t *buf, int len)
{
    // 检查数据长度是否足够（至少包含地址+功能码+字节数+CRC）
    if (len < 5) {
        printf("错误：数据长度不足，需要至少5字节，实际收到%d字节\n", len);
        return 0;
    }

    // 跳过 Modbus RTU 帧头 (地址1字节 + 功能码1字节 + 字节数1字节)
    const uint8_t *data = buf + 3;
    int dataLen = len - 5; // 去掉帧头3字节和CRC校验2字节

    // 宏定义：从数据中提取16位和32位寄存器值
    // 注意：Modbus使用大端字节序（高字节在前）
    #define REG16(i)   ((data[(i)*2] << 8) | data[(i)*2+1])        // 提取16位寄存器
    #define REG32(i)   ((REG16(i+1) << 16) | REG16(i))             // 提取32位寄存器（低字在后）

    // 提取正累计流量 (寄存器REG1464–1465 对应数据偏移22,23)
    int posRaw = REG32(22);

    // 提取净累计流量 (寄存器REG1443–1444 对应数据偏移1,2)
    int netRaw = REG32(1);

    // 计算负累计流量 = 正累计流量 - 净累计流量
    int negRaw = posRaw - netRaw;

    // 提取瞬时流量 (IEEE754浮点格式, 寄存器REG1447–1448 对应数据偏移5,6)
    // 注意：IEEE754遵从低位低字节在前排放原则，所以REG16(5)是低字，REG16(6)是高字
    uint32_t instRaw = ((uint32_t)REG16(6) << 16) | REG16(5);
    float instVal;
    memcpy(&instVal, &instRaw, sizeof(float));  // 将32位整数转换为IEEE754浮点数

    // 提取流速 (IEEE754浮点格式, 寄存器REG1449–1450 对应数据偏移7,8)
    // 注意：IEEE754遵从低位低字节在前排放原则，所以REG16(7)是低字，REG16(8)是高字
    uint32_t velRaw = ((uint32_t)REG16(8) << 16) | REG16(7);
    float velVal;
    memcpy(&velVal, &velRaw, sizeof(float));    // 将32位整数转换为IEEE754浮点数

    // 提取电池电压 (IEEE754浮点格式, 寄存器REG1455–1456 对应数据偏移13,14)
    // 注意：IEEE754遵从低位低字节在前排放原则，所以REG16(13)是低字，REG16(14)是高字
    uint32_t battRaw = ((uint32_t)REG16(14) << 16) | REG16(13);
    float battVal;
    memcpy(&battVal, &battRaw, sizeof(float));  // 将32位整数转换为IEEE754浮点数

    // 提取ESN设备序列号 (BCD编码格式, 寄存器REG1466–1467 对应数据偏移24,25)
    uint32_t esnRaw = ((uint32_t)REG16(25) << 16) | REG16(24);

    // 将BCD编码的ESN转换为字符串
    char tmp[11] = {0};  // 临时缓冲区，最多8位BCD数字+结束符
    int idx = 0;

    // 从高位到低位提取每个BCD数字（每4位表示一个十进制数字）
    for (int i = 7; i >= 0; i--) {
        int digit = (esnRaw >> (i*4)) & 0xF;  // 提取第i个4位BCD数字
        tmp[idx++] = '0' + digit;             // 转换为ASCII字符
    }
    tmp[idx] = '\0';  // 字符串结束符

    // 去掉前导零，保留至少一位数字
    int start = 0;
    while (tmp[start] == '0' && start < idx-1) start++;
    strcpy(g_esnStr, tmp + start);  // 复制到全局ESN字符串变量

    // 将解析结果存储到全局变量中
    g_posFlow = posRaw;           // 正累计流量（整数）
    g_negFlow = negRaw;           // 负累计流量（整数）
    g_netFlow = netRaw;           // 净累计流量（整数）
    g_instFlow = instVal;         // 瞬时流量（浮点型，保持原精度）
    g_velocity = velVal;          // 流速（浮点型，保持原精度）
    g_batteryVoltage = battVal;   // 电池电压（浮点型，保持原精度）

    // 解析成功，设置成功标志
    g_water_meter_read_flag = 1;
    printf("数据解析成功，成功标志已设置为1\n");

    return 1;  // 返回成功
}

/**
 * @brief 主函数 - 演示水表数据读取和解析流程
 */
int daoSheng_protocol_parsing(void) {
    int retry_count = 0;  // 重试计数器
    const int MAX_RETRY = 2;  // 最大重试次数
    uint8_t recvData[256];  // 接收数据缓冲区
    int len = 0;  // 接收数据长度

    // 初始化成功标志为0
    g_water_meter_read_flag = 0;

    // 为了测试失败情况，可以临时修改数据长度来模拟失败
    // int test_len = 3;  // 取消注释这行来测试失败情况

    // 读取和解析水表数据，支持重试机制
    do {
        retry_count++;
        printf("\n=== 第%d次尝试读取水表数据 ===\n", retry_count);

        // 清空接收缓冲区
        uart_flush();

        // 发送读取水表数据命令
        sendWaterMeterReadCommand();

        // 等待响应数据
        printf("等待水表响应...\n");
        int wait_time = 0;
        const int MAX_WAIT_TIME = 1000; // 最大等待时间1000ms

        while(wait_time < MAX_WAIT_TIME) {
            if(uart_available() > 0) {
                // 有数据可读，接收数据
                len = uart_receive(recvData, sizeof(recvData));
                if(len > 0) {
                    printf("接收到%d字节数据: ", len);
                    for(int i = 0; i < len; i++) {
                        printf("%02X ", recvData[i]);
                    }
                    printf("\n");
                    break;
                }
            }
            // 延时10ms后再检查
            HAL_Delay(10);
            wait_time += 10;
        }

        if(len == 0) {
            printf("超时：未接收到水表响应数据\n");
        } else {
            // 解析接收到的数据，结果存储在全局变量中
            if(parseWaterMeterData(recvData, len)) {
                printf("第%d次尝试：数据解析成功！\n", retry_count);
                break;  // 解析成功，跳出循环
            } else {
                printf("第%d次尝试：数据解析失败！\n", retry_count);
            }
        }

        // 如果达到最大重试次数，清零所有数据并退出
        if(retry_count >= MAX_RETRY) {
            clearWaterMeterData();  // 清零所有水表数据
            printf("错误：已达到最大重试次数(%d次)，水表读取失败！\n", MAX_RETRY);
            break;
        } else {
            printf("准备进行第%d次重试...\n", retry_count + 1);
        }
    } while(retry_count < MAX_RETRY);

    // 打印最终结果
    printf("\n=== 水表数据读取结果 ===\n");
    printf("读取状态: %s\n", g_water_meter_read_flag ? "成功" : "失败");

    if(g_water_meter_read_flag) {
        // 从全局变量读取解析结果
        printf("正累计流量: %d\n", g_posFlow);
        printf("负累计流量: %d\n", g_negFlow);
        printf("净累计流量: %d\n", g_netFlow);
        printf("瞬时流量: %.2f\n", g_instFlow);      // 浮点型，保留两位小数
        printf("流速: %.2f\n", g_velocity);          // 浮点型，保留两位小数
        printf("电池电压: %.2fV\n", g_batteryVoltage); // 浮点型，保留两位小数
        printf("ESN: %s\n", g_esnStr);
    } else {
        printf("由于读取失败，无法获取有效的水表数据\n");
        printf("所有数据已清零：正累计=%d, 负累计=%d, 净累计=%d, 瞬时=%.2f, 流速=%.2f, 电压=%.2fV\n",
               g_posFlow, g_negFlow, g_netFlow, g_instFlow, g_velocity, g_batteryVoltage);
    }

    printf("程序执行完毕\n");
    return 0;
}


//485水表数据采集功能已经调试完成，可调用全局变量直接使用
//执行结果示例：
/**
=== 第1次尝试读取水表数据 ===
发送读取命令: 01 03 05 A1 00 1C 15 2D
等待水表响应（模拟延时1s）
数据解析成功，成功标志已设置为1
第1次尝试：数据解析成功！

=== 水表数据读取结果 ===
读取状态: 成功
正累计流量: 820
负累计流量: 0
净累计流量: 820
瞬时流量: 820.00
流速: 820.00
电池电压: 3.57V
ESN: 90112211
程序执行完毕
 */


