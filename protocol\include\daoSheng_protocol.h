/**
 * @file daoSheng_protocol.h
 * @brief 大连道盛水表协议头文件
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */

#ifndef __DAO_SHENG_PROTOCOL_H__
#define __DAO_SHENG_PROTOCOL_H__

#include "system.h"
#include <stdint.h>

// 串口数据结构定义（兼容旧代码）
typedef struct {
    uint8_t rx_buffer[256];  // 接收缓冲区
    uint8_t rx_count;        // 接收数据长度
} SERIAL;

// 水表数据结构定义（兼容旧代码）
typedef struct {
    float flow_rate;         // 瞬时流量(m³/s)
    float velocity;          // 流速(m/s)
    float battery_voltage;   // 电池电压(V)
    double net_accumulated;   // 净累积量
    uint32_t work_status;    // 工作状态码
    char software_version[5];// 软件版本
} MeterData;

// 函数声明
/**
 * @brief 大连道盛协议解析主函数
 * @return 0-成功，其他-失败
 */
int daoSheng_protocol_parsing(void);

/**
 * @brief 发送读取水表数据的固定报文
 */
void sendWaterMeterReadCommand(void);

/**
 * @brief 解析水表数据
 * @param buf 接收到的数据缓冲区
 * @param len 数据长度
 * @return 1-解析成功，0-解析失败
 */
int parseWaterMeterData(const uint8_t *buf, int len);

/**
 * @brief 清零所有水表数据
 */
void clearWaterMeterData(void);

// 兼容旧接口的函数声明
void parse_modbus_data(uint8_t *data, uint16_t len, MeterData *result);
uint16_t calculate_crc16(const uint8_t *data, uint16_t length);
uint16_t mbus_crc16(uint8_t *data, uint16_t length);
void get_water_value(void);

#endif
