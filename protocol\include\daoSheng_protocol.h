/**
 * @file daoSheng_protocol.h
 * @brief 大连道盛水表协议头文件
 * <AUTHOR> Assistant
 * @date 2025-08-30
 */

#ifndef __DAO_SHENG_PROTOCOL_H__
#define __DAO_SHENG_PROTOCOL_H__

#include <stdint.h>

/**
 * @brief 大连道盛协议解析主函数
 * @return 0-成功，其他-失败
 */
int daoSheng_protocol_parsing(void);

/**
 * @brief 发送读取水表数据的固定报文
 */
void sendWaterMeterReadCommand(void);

/**
 * @brief 解析水表数据
 * @param buf 接收到的数据缓冲区
 * @param len 数据长度
 * @return 1-解析成功，0-解析失败
 */
int parseWaterMeterData(const uint8_t *buf, int len);

/**
 * @brief 清零所有水表数据
 */
void clearWaterMeterData(void);

#endif
