#include "net_protocol.h"
#include "net.h"
#include <string.h>
#include "main.h"
#include "rtc.h"
#include "stm32_flash.h"
#include "user_config.h"


#define SF0 0
#define SF1 1
#define BD_ADDR 2
#define PC_ADDR 7
#define PASSWORD_H 8
#define PASSWORD_L 9
#define CMD 0x0A
#define DIR 0x0B
#define PACK 0x0D
#define DATA 0x0E

#define HEAD_SF1 0x7E		// 包头1
#define HEAD_SF2 0x7E		// 包头2
#define PC_END_SF1 0x05	// pc结束符
#define PC_END_SF2 0x04 // pc结束符
#define BD_END_SF 0x03	// bd结束符
#define PC_PACK_SF 0x80 // 下发
#define BD_PAGE_SF 0x00 // 上报
#define PACK_SF 0x02		// 报文起始符

#define CMD_SET_ADDRESS 0x70 // 设置遥测地址
#define CMD_SET_TIME 0x71		 // 设置时钟
#define CMD_GET_TIME 0x72		 // 获取时钟
#define CMD_INIT 0x73				 // 初始化
#define CMD_SET_IP_SIM 0x74	 // 设置ip和sim卡
#define CMD_GET_IP_SIM 0x75	 // 获取ip和sim卡
#define CMD_SET_PARAM 0x76	 // 设置参数
#define CMD_GET_PARAM 0x77	 // 获取参数

#define CMD_TIMING_UPLOAD 0x78 // 定时上传

static uint8_t hex_buffer[128];

/**
 * 计算Modbus CRC16校验
 * @param data 输入数据指针
 * @param length 数据长度
 * @return 16位CRC校验值
 */
static uint16_t modbus_crc16(const uint8_t *data, uint16_t length)
{
	uint16_t crc = 0xFFFF;
	for (uint16_t i = 0; i < length; i++)
	{
		crc ^= data[i];
		for (uint8_t j = 0; j < 8; j++)
		{
			if (crc & 0x0001)
				crc = (crc >> 1) ^ 0xA001;
			else
				crc = crc >> 1;
		}
	}
	return crc;
}

// 将十进制数转换为十六进制数，并返回一个字节
static uint8_t dec_to_hex_to_byte(uint8_t dec)
{
	// 将十进制数除以10取余数，得到个位数
	return (dec % 10) + ((dec / 10 % 10) * 16);
}
/**
 * 10进制转16进制如：
 * 123456-》-0x12，,0x34,0x56
 */

static void dec_to_hex(uint32_t dec, uint8_t *hex, uint8_t size)
{
	// 遍历每个字节位置
	for (uint8_t i = 0; i < size; i++)
	{
		// 计算当前字节位置的十六进制值
		// 将十进制数的个位数转换为十六进制值，并与十位数转换的十六进制值合并
		hex[size - 1] = (dec % 10) + ((dec / 10 % 10) * 16);
		// 去掉已经转换过的最低两位
		dec /= 100;
	}
}
/**
 * 16进制转10进制如：
 * 0x12，0x34,0x56-》123456
*/
static void hex_to_dec(uint8_t *hex,uint64_t *dec,uint8_t hex_size)
{
	*dec = 0;
	for(uint8_t i = 0; i < hex_size; i++)
	{	
		*dec *= 100;
		*dec += (hex[i] % 16) + ((hex[i] / 16) * 10);
	}
}

static uint16_t get_hex_data(void)
{
	uint16_t len = 0;
	memset(hex_buffer, 0, sizeof(hex_buffer));
	memcpy(hex_buffer,net_info.serial_port->recv, net_info.serial_port->count);
	/*数据解析完成后，清空缓存，可以定义新缓存，拷贝缓存后清空数据*/
	net_info.serial_port->clean();
	return len;
}

static uint8_t net_data_parsing(void)
{
	uint16_t len = get_hex_data();
	if (len == 0)
		return 0;
	uint8_t res = 0;
	uint16_t crc = 0;
	if (hex_buffer[SF0] != HEAD_SF1) // 包头校验
		goto end;
	if (hex_buffer[SF1] != HEAD_SF2) // 包头校验
		goto end;
	// if (hex_buffer[PC_ADDR] != net_config.central_station_address)//中心站地址校验
	// 	goto end;
	if((hex_buffer[PASSWORD_H] != config.net_param.password[0]) || (hex_buffer[PASSWORD_L] != config.net_param.password[1]))//密码校验
		goto end;
	if (hex_buffer[DIR] != PC_PACK_SF) // 下发方向校验
		goto end;
	if (hex_buffer[PACK] != PACK_SF) // 报文起始符校验
		goto end;
	if ((hex_buffer[len - 3] != PC_END_SF1) || (hex_buffer[len - 3] != PC_END_SF2)) // 报文结束符校验
		goto end;
	crc = modbus_crc16(hex_buffer, len - 2);
	if ((hex_buffer[len - 1] != ((crc >> 8) & 0xFF)) || (hex_buffer[len - 2] != (crc & 0xFF))) // CRC校验
		goto end;

	res = 1;
end:
	if (res == 0x00)
		memset(hex_buffer, 0, sizeof(hex_buffer)); // 数据不符合则清空
	return res;
}

static void net_send_packet(uint8_t addr,uint8_t cmd,uint8_t *data_buf,uint16_t data_len)
{
	uint8_t send_buf[80],len = 0;
	memset(send_buf, 0, sizeof(send_buf));
	send_buf[len++] = HEAD_SF1;
	send_buf[len++] = HEAD_SF2;
	send_buf[len++] = addr;//config.net_param.central_station_address;
	for(uint8_t i = 0; i < 5; i++)
		send_buf[len++] = config.net_param.telemetry_address[i];
	send_buf[len++] = config.net_param.password[0];
	send_buf[len++] = config.net_param.password[1];
	send_buf[len++] = cmd;
	send_buf[len++] = BD_PAGE_SF;
	send_buf[len++] = 0;//长度，最后赋值
	send_buf[len++] = PACK_SF;
	send_buf[len++] = hex_buffer[DATA];
	send_buf[len++] = hex_buffer[DATA+1];
	send_buf[len++] = dec_to_hex_to_byte(calendar.year - 2000);
	send_buf[len++] = dec_to_hex_to_byte(calendar.month);
	send_buf[len++] = dec_to_hex_to_byte(calendar.day);
	send_buf[len++] = dec_to_hex_to_byte(calendar.hour);
	send_buf[len++] = dec_to_hex_to_byte(calendar.minute);
	send_buf[len++] = dec_to_hex_to_byte(calendar.second);
	send_buf[len++] = 0xF1;
	send_buf[len++] = 0xF1;
	for(uint8_t i = 0; i < 5; i++)
	{
		send_buf[len++] = config.net_param.telemetry_address[i];
	}

	for(uint16_t i = 0;i < data_len;i++)
	{
		send_buf[len++] = data_buf[i];
	}
	send_buf[len++] = BD_END_SF;
	send_buf[PACK - 1] = len;
	uint16_t crc = modbus_crc16(send_buf, len);
	send_buf[len++] = (crc >> 8) & 0xFF;
	send_buf[len++] = crc & 0xFF;

	net_info.tcpip->send(send_buf, len);
}

// 设置遥测地址
static void _cmd_set_address_(void)
{
	uint8_t buf[1],len = 0;
	memcpy(config.net_param.telemetry_address,hex_buffer + DATA + 8,5);
	config_refresh();
	net_send_packet(hex_buffer[PC_ADDR],CMD_SET_ADDRESS,buf, len);
}
// 设置时钟
static void _cmd_set_time_(void)
{
	uint8_t buf[1],len = 0;
	uint8_t offset = DATA + 2;

	rtc_set_time(hex_buffer[offset + 0] + 2000,hex_buffer[offset + 1],hex_buffer[offset + 2],hex_buffer[offset + 3],hex_buffer[offset + 4],hex_buffer[offset + 5]);

	net_send_packet(hex_buffer[PC_ADDR],CMD_SET_TIME,buf, len);
}
// 获取时钟
static void _cmd_get_time_(void)
{
	uint8_t buf[1],len = 0;
	
	net_send_packet(hex_buffer[PC_ADDR],CMD_GET_TIME,buf, len);
}
/**
 * 初始化
 * 在执行中心站初始化遥测站命令前， 要有提示： 执行此命令会清空采集器内的全部历史数据， 请慎重执行！
在用户选择确认后再执行中心站初始化遥测站命令， 采集器接收到命令后把历史数据记录全部清空
*/
static void _cmd_init_(void)
{
	uint8_t buf[1],len = 0;
	uint8_t offset = DATA + 8;

	if((hex_buffer[offset] != 0x97) || (hex_buffer[offset + 1] != 0x00))//(初始化遥测站标示符不符合直接退出返回)
		return;
	read_config(1);//配置数据恢复默认值

	net_send_packet(hex_buffer[PC_ADDR],CMD_INIT,buf, len);
}
/**
* @brief 获取网络信息
*
* 从缓冲区中获取网络信息，包括IP地址、端口号和SIM卡号，并填充到配置结构体中。
*
* @return 返回值1表示成功，0表示失败（当设备数量超过3时）
*/
/**************************** CodeGeeX Inline Diff ****************************/
/**
 * @brief 从十六进制缓冲区中提取网络配置信息
 * 
 * 该函数从hex_buffer中提取指定编号的网络配置信息，包括IP地址、端口号和SIM卡信息。
 * 网络配置信息最多支持4组（编号0-3）。
 * 
 * @param void 无参数
 * @return uint8_t 返回1表示成功获取网络信息，返回0表示失败（编号超出范围）
 * 
 * @note 内部使用hex_buffer作为输入源，使用config.net_param存储结果
 * @note IP地址在hex_buffer中以7位十进制数存储，需要转换为4字节的IP地址格式
 * @note 端口号以3位十进制数存储
 * @note SIM卡信息仅在编号为0时提取
 */
/**
 * 获取网络信息配置函数
 * 
 * 该函数从hex_buffer中解析网络参数信息，包括IP地址和端口号，
 * 并将解析结果存储到config.net_param结构体中
 * 
 * @return uint8_t 返回1表示解析成功，返回0表示解析失败
 */
static uint8_t get_net_info(void)
{
	/* 计算网络参数序号，偏移地址PC_ADDR处的值减1得到序号 */
	u8 no = (hex_buffer[PC_ADDR] - 0x01);
	
	/* 检查序号是否在有效范围内(0-3)，超出范围则返回错误 */
	if(no >= 4)
		return 0;

	uint64_t ip;
	
	/* 从hex_buffer中提取IP地址数据并转换为十进制 */
	hex_to_dec(hex_buffer + DATA + 8, &ip,7);
	
	/* 将十进制IP地址按字节分离并存储 */
	uint8_t ip_temp[4];
	for(u8 i = 0;i < 4;i++)
	{
		ip_temp[3-i] = ip % 1000;
		ip /= 1000;
	}
	
	/* 将解析出的IP地址复制到配置结构体中对应位置 */
	memcpy(config.net_param.ip[no], ip_temp, 4);
	
	uint16_t port;
	
	/* 从hex_buffer中提取端口数据并转换为十进制 */
	hex_to_dec(hex_buffer + DATA + 15, (uint64_t *)&port, 3);
	
	/* 将解析出的端口号存储到配置结构体中对应位置 */
	config.net_param.port[no] = port;
	
	/* 如果是第一个网络参数(NO.0)，则同时解析SIM卡信息 */
	if(no == 0)
	{
		memcmp(config.net_param.sim,hex_buffer + DATA + 18,sizeof(config.net_param.sim));
	}
	
	return 1;
}


/**
 * 设置网络信息
 * 
 * 该函数根据给定的网络参数编号，将对应的IP地址和端口号转换为十六进制格式
 * 
 * @param no 网络参数编号(0-3)，超过3则返回错误
 * @param ip_hex 用于存储IP地址十六进制格式的缓冲区指针
 * @param ip_hex_size IP地址十六进制缓冲区大小
 * @param port_hex 用于存储端口号十六进制格式的缓冲区指针
 * @param port_hex_size 端口号十六进制缓冲区大小
 * 
 * @return uint8_t 操作结果，1表示成功，0表示失败
 */
static uint8_t set_net_info(uint8_t no,uint8_t *ip_hex,uint8_t ip_hex_size,uint8_t *port_hex,uint8_t port_hex_size)
{
	// 检查网络参数编号是否有效
	if (no > 3)
		return 0;
		
	// 复制指定编号的IP地址和端口号到本地变量
	uint8_t ip[4];
	memcpy(ip, config.net_param.ip[no], 4);
	uint16_t port = config.net_param.port[no];
	
	// 将IP地址的十进制数值转换为特定格式的数值
	uint64_t temp = 0;
	for (uint8_t i = 0; i < 4; i++)
	{
		temp *= 1000;
		temp += ip[3 - i];
	}
	
	// 将IP地址和端口号转换为十六进制格式并存储到输出缓冲区
	dec_to_hex(temp, ip_hex, ip_hex_size);
	dec_to_hex(port, port_hex, port_hex_size);
	return 1;
} 

// 设置ip和sim卡
/**
 * 设置IP和SIM卡信息命令处理函数
 * 
 * 该函数用于处理设置IP地址和SIM卡信息的命令，包括获取网络信息、
 * 设置网络参数、组装数据包并发送给目标设备。
 * 
 * 参数: 无
 * 返回值: 无
 */
static void _cmd_set_ip_sim_(void)
{
	/* 定义数据缓冲区和长度变量 */
	uint8_t buf[20],len = 0;

	/* 获取网络信息，如果成功则刷新配置 */
	if(get_net_info() == 1)
	{
		config_refresh();//正确获取数据后刷新
	}
	
	/* 清空缓冲区，准备组装数据包 */
	memset(buf,0,sizeof(buf));
	uint8_t ip[7],port[3];
	
	/* 设置网络信息，包括IP和端口，失败则直接返回 */
	if(set_net_info(hex_buffer[PC_ADDR] - 0x01,ip,sizeof(ip),port,sizeof(port)) != 1)
		return;
	
	/* 将IP地址、端口和SIM卡参数依次组装到发送缓冲区 */
	for(uint8_t i = 0; i < 7; i++)
	{
		buf[len++] = ip[i];
	}
	for(uint8_t i = 0; i < 3; i++)
	{
		buf[len++] = port[i];
	}
	for(uint8_t i = 0; i < 8; i++)
	{
		buf[len++] = config.net_param.sim[i];
	}
	
	/* 发送组装好的数据包 */
	net_send_packet(hex_buffer[PC_ADDR],CMD_SET_IP_SIM,buf, len);
}
// 获取ip和sim卡
/**
 * 获取IP和SIM卡信息并发送响应包
 * 
 * 该函数用于获取网络IP地址、端口和SIM卡参数信息，
 * 并将这些信息打包发送给请求方。
 * 
 * 参数: 无
 * 返回值: 无
 */
static void _cmd_get_ip_sim_(void)
{
	/* 定义缓冲区和长度变量 */
	uint8_t buf[20],len = 0;
	
	/* 初始化缓冲区 */
	memset(buf, 0, sizeof(buf));
	
	/* 定义IP地址和端口存储数组 */
	uint8_t ip[7],port[3];
	
	/* 获取网络信息，如果失败则直接返回 */
	if(set_net_info(hex_buffer[PC_ADDR] - 0x01,ip,sizeof(ip),port,sizeof(port)) != 1)
		return;
	
	/* 将IP地址复制到发送缓冲区 */
	for(uint8_t i = 0; i < 7; i++)
	{
		buf[len++] = ip[i];
	}
	
	/* 将端口信息复制到发送缓冲区 */
	for(uint8_t i = 0; i < 3; i++)
	{
		buf[len++] = port[i];
	}
	
	/* 将SIM卡参数复制到发送缓冲区 */
	for(uint8_t i = 0; i < 8; i++)
	{
		buf[len++] = config.net_param.sim[i];
	}
	
	/* 发送数据包 */
	net_send_packet(hex_buffer[PC_ADDR],CMD_GET_IP_SIM,buf, len);
}
// 设置参数
/**
 * @brief 设置参数命令处理函数
 * 
 * 该函数用于处理来自PC的设置参数命令，解析命令中的数据并更新配置信息，
 * 然后将设置的参数回传给PC以作确认。
 * 
 * 函数流程：
 * 1. 根据地址判断是第几个参数组（no）；
 * 2. 解析命令中携带的数据，并保存到config结构体中；
 * 3. 调用config_refresh刷新配置；
 * 4. 将设置的参数重新打包并通过网络发送回PC。
 * 
 * @note 本函数为静态函数，仅在当前文件内使用。
 */
static void _cmd_set_param_(void)
{
	u8 no = hex_buffer[PC_ADDR] - 0x01;
	if(no > 3)
		return;
	uint8_t buf[16],len = 0;
	uint8_t temp[5];

	/* 解析命令数据并保存至配置结构体 */
	{
		hex_to_dec(hex_buffer + DATA + 8,(uint64_t *)&config.net_param.upload_cycle[no],2);
		hex_to_dec(hex_buffer + DATA + 10,(uint64_t *)&config.water_meter_info.cycle,3);
		hex_to_dec(hex_buffer + DATA + 13,(uint64_t *)&config.water_meter_info.protocol_type,1);
		config.water_meter_info.baud_rate_id = (BAUD_RATE_ID)hex_buffer[DATA + 14];
		config.water_meter_info.data_bit_id = (DATA_BIT_ID)hex_buffer[DATA + 15];
		config.water_meter_info.stop_bit_id = (STOP_BIT_ID)hex_buffer[DATA + 16];
		config.water_meter_info.parity_id = (PARITY_ID)hex_buffer[DATA + 17];
		config_refresh();
	}
	
	memset(buf, 0, sizeof(buf));
	
	memset(temp, 0, sizeof(temp));

	/* 将配置参数转换为HEX格式并填入发送缓冲区 */
	dec_to_hex(config.net_param.upload_cycle[no],temp,2);
	buf[len++] = temp[0];
	buf[len++] = temp[1];
	memset(temp, 0, sizeof(temp));
	dec_to_hex(config.water_meter_info.cycle,temp,3);
	for(uint8_t i = 0; i < 3; i++)
	{
		buf[len++] = temp[i];
	}
	memset(temp, 0, sizeof(temp));
	dec_to_hex(config.water_meter_info.protocol_type,temp,1);
	buf[len++] = temp[0];
	buf[len++] = config.water_meter_info.protocol_type;
	buf[len++] = config.water_meter_info.baud_rate_id;
	buf[len++] = config.water_meter_info.data_bit_id;
	buf[len++] = config.water_meter_info.stop_bit_id;
	buf[len++] = config.water_meter_info.parity_id;
	buf[len++] = 0;
	buf[len++] = 0;
	buf[len++] = 0;
	buf[len++] = 0;

	/* 发送响应包到PC */
	net_send_packet(hex_buffer[PC_ADDR],CMD_SET_PARAM,buf,len);
}
// 获取参数
/**
 * @brief 获取参数命令处理函数
 * 
 * 该函数用于处理获取参数的命令请求，将设备配置参数打包并通过网络发送回PC端。
 * 主要包含上传周期、水表信息等相关配置参数。
 * 
 * @param 无
 * @return 无
 */
static void _cmd_get_param_(void)
{
	// 从地址字节解析参数编号，减去0x01得到实际索引
	u8 no = hex_buffer[PC_ADDR] - 0x01;
	
	// 参数编号有效性检查，只允许0-3范围内的编号
	if(no > 3)
		return;
		
	// 定义数据缓冲区和长度计数器
	uint8_t buf[16],len = 0;
	uint8_t temp[5];

	// 初始化缓冲区
	memset(buf, 0, sizeof(buf));
	memset(temp, 0, sizeof(temp));
	
	// 将指定编号的上传周期参数转换为HEX格式并存入缓冲区
	dec_to_hex(config.net_param.upload_cycle[no],temp,2);
	buf[len++] = temp[0];
	buf[len++] = temp[1];
	
	// 清空临时缓冲区，准备处理水表周期参数
	memset(temp, 0, sizeof(temp));
	
	// 将水表周期参数转换为HEX格式并存入缓冲区
	dec_to_hex(config.water_meter_info.cycle,temp,3);
	for(uint8_t i = 0; i < 3; i++)
	{
		buf[len++] = temp[i];
	}
	
	// 清空临时缓冲区，准备处理协议类型参数
	memset(temp, 0, sizeof(temp));
	
	// 将协议类型参数转换为HEX格式并存入缓冲区
	dec_to_hex(config.water_meter_info.protocol_type,temp,1);
	buf[len++] = temp[0];
	
	// 直接将水表相关信息存入缓冲区
	buf[len++] = config.water_meter_info.protocol_type;
	buf[len++] = config.water_meter_info.baud_rate_id;
	buf[len++] = config.water_meter_info.data_bit_id;
	buf[len++] = config.water_meter_info.stop_bit_id;
	buf[len++] = config.water_meter_info.parity_id;
	
	// 填充保留字节
	buf[len++] = 0;
	buf[len++] = 0;
	buf[len++] = 0;
	buf[len++] = 0;
	
	// 发送参数数据包到PC端
	net_send_packet(hex_buffer[PC_ADDR],CMD_GET_PARAM,buf,len);
}

static uint16_t tcpip_disconnect_timeout = 0;
static uint8_t tcpip_connect_flag = 0;
/**
 * @brief 处理TCP/IP连接的时序控制和状态管理
 * 
 * 该函数根据接收数据包中的特定字节值来控制TCP/IP连接的状态，
 * 包括设置连接超时和直接关闭连接的操作。
 * 
 * @param 无
 * @return 无
 */
static void _cmd_timing_upload_(void)
{
	/* 根据数据包内容判断是否需要设置TCP/IP连接超时或直接关闭连接 */
	if((hex_buffer[DATA + 8] == 0xAA) && (hex_buffer[PC_ADDR] == 0x01))/*等待超时关闭tcpip，socket*/
	{
		/* 当检测到特定标志位时，记录当前系统时间作为连接超时的起始时间 */
		tcpip_disconnect_timeout = HAL_GetTick();
	}else if(hex_buffer[DATA + 8] == 0x00)
	{
		/* 当检测到关闭连接标志时，清除连接标志并直接关闭TCP/IP连接 */
		tcpip_connect_flag = 0;
		net_info.tcpip->close();//直接关闭tcpip
	}
}

/**
 * @brief 网络协议解析函数
 * @details 该函数负责解析串口接收到的网络协议数据，根据命令类型执行相应的处理函数，
 *          并监测TCP/IP连接的断线条件
 * @param 无
 * @return 无
 */
void net_protocol_parsing(void)
{
	/* 检查串口数据接收完成条件：接收计数不为0且字节超时为0 */
	if (net_info.serial_port->count && !net_info.serial_port->byte_timeout) // 表明已经接收到一组数据，可以对数据进行解析
	{
		/*网络协议解析*/
		/* 调用数据解析函数，解析成功则根据命令类型执行相应处理 */
		if (net_data_parsing())
		{
			/* 根据命令码执行对应的命令处理函数 */
			switch (hex_buffer[CMD])
			{
				case CMD_SET_ADDRESS: _cmd_set_address_();break;
				case CMD_SET_TIME: _cmd_set_time_();break;
				case CMD_GET_TIME: _cmd_get_time_();break;
				case CMD_INIT: _cmd_init_();break;
				case CMD_SET_IP_SIM: _cmd_set_ip_sim_();break;
				case CMD_GET_IP_SIM: _cmd_get_ip_sim_();break;
				case CMD_SET_PARAM: _cmd_set_param_();break;
				case CMD_GET_PARAM: _cmd_get_param_();break;
				case CMD_TIMING_UPLOAD: _cmd_timing_upload_();break;
				default: break;
			}
			/* 重置TCP/IP断线超时计时器 */
			tcpip_disconnect_timeout = TCPIP_DISCONNECT_TIMEOUT;
		}
		/* 清空数据缓冲区 */
		memset(hex_buffer, 0, sizeof(hex_buffer));
	}
	/*监测断线条件*/
	/* 检查TCP/IP连接是否超时，如果超时则断开连接 */
	if((tcpip_connect_flag == 1) && ((HAL_GetTick() - tcpip_disconnect_timeout) > TCPIP_DISCONNECT_TIMEOUT))
	{
		tcpip_connect_flag = 0;
		net_info.tcpip->close();//直接关闭tcpip
	}
}

/**
 * 心跳，定时上报数据
 * 如果采集器无法读取水表表号， 则按零上报
	7E 7E 
	02 
	65 32 23 2D 92 
	00 00 
	78 
	00 31 
	02 
	00 04 
	25 08 01 16 15 22 
	F1 F1 
	65 32 23 2D 92 
	00 00 33 22 32 
	00 00 00 00 12 
	00 00 33 22 20 
	00 00 33 11 97 
	00 23 
	0A 
	12 35 
	00 00 33 22 11 00 
	00 
	00 00 
	03 
	85 A0
*/
/**
 * @brief 网络心跳包发送函数，用于向中心站发送当前设备的状态和数据信息。
 *
 * 该函数构建一个包含时间、地址、流量、信号强度、电池电量等信息的心跳包，
 * 并通过网络发送给指定的中心站。
 *
 * @param central_station_address 中心站地址，用于标识数据发送的目标地址。
 * @param data 包含待上传数据的结构体，包括累计流量、瞬时流量、信号强度、电池电量等信息。
 *
 * @return 返回值始终为0，表示函数执行成功。
 */
uint8_t net_heartbeat(uint8_t central_station_address, UPLOAD_DATA data)
{
    tcpip_connect_flag = 1; // 标记TCP/IP连接已建立

    uint8_t send_buf[64], len = 0;
    uint8_t temp[10];
    memset(send_buf, 0, sizeof(send_buf));

    // 构建心跳包头部信息
    send_buf[len++] = 0x00;
    send_buf[len++] = 0x00; // 流水号，待机唤醒后复位，非保存数据无效
    send_buf[len++] = dec_to_hex_to_byte(calendar.year - 2000);
    send_buf[len++] = dec_to_hex_to_byte(calendar.month);
    send_buf[len++] = dec_to_hex_to_byte(calendar.day);
    send_buf[len++] = dec_to_hex_to_byte(calendar.hour);
    send_buf[len++] = dec_to_hex_to_byte(calendar.minute);
    send_buf[len++] = dec_to_hex_to_byte(calendar.second);
    send_buf[len++] = 0xF1;
    send_buf[len++] = 0xF1;

    // 添加遥测地址
    for(uint8_t i = 0; i < 5; i++)
    {
        send_buf[len++] = config.net_param.telemetry_address[i];
    }

    // 添加正向累计流量（5字节）
    memset(temp, 0, sizeof(temp));
    dec_to_hex(data.positive_cumulative_flow, temp, 5);
    for(uint8_t i = 0; i < 5; i++)
    {
        send_buf[len++] = temp[i];
    }

    // 添加反向累计流量（5字节）
    memset(temp, 0, sizeof(temp));
    dec_to_hex(data.negative_cumulative_flow, temp, 5);
    for(uint8_t i = 0; i < 5; i++)
    {
        send_buf[len++] = temp[i];
    }

    // 添加净累计流量（5字节）
    memset(temp, 0, sizeof(temp));
    dec_to_hex(data.net_cumulative_flow_rate, temp, 5);
    for(uint8_t i = 0; i < 5; i++)
    {
        send_buf[len++] = temp[i];
    }

    // 添加瞬时流量（5字节）
    memset(temp, 0, sizeof(temp));
    dec_to_hex(data.instantaneous_flow, temp, 5);
    for(uint8_t i = 0; i < 5; i++)
    {
        send_buf[len++] = temp[i];
    }

    // 添加瞬时流量速率（2字节）
    memset(temp, 0, sizeof(temp));
    dec_to_hex(data.instantaneous_flow_rate, temp, 2);
    for(uint8_t i = 0; i < 2; i++)
    {
        send_buf[len++] = temp[i];
    }

    // 添加网络信号强度
    send_buf[len++] = data.net_sig;

    // 添加电池电压（2字节）
    memset(temp, 0, sizeof(temp));
    dec_to_hex(data.battery, temp, 2);
    for(uint8_t i = 0; i < 2; i++)
    {
        send_buf[len++] = temp[i];
    }

    // 添加水表编号（6字节）
    memset(temp, 0, sizeof(temp));
    dec_to_hex(data.water_meter_number, temp, 6);
    for(uint8_t i = 0; i < 6; i++)
    {
        send_buf[len++] = temp[i];
    }

    // 添加报警状态和保留字段
    send_buf[len++] = data.warning;
    send_buf[len++] = 0x00;
    send_buf[len++] = 0x00;

    // 清空发送缓冲区并发送数据包
    memset(hex_buffer, 0, sizeof(hex_buffer));
    net_info.serial_port->clean();
    net_send_packet(central_station_address, CMD_TIMING_UPLOAD, send_buf, len);

    return 0;
}
