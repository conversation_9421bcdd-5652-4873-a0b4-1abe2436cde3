.\objects\bsp_board.o: ..\drivers\bsp_board.c
.\objects\bsp_board.o: ..\drivers\include\bsp_board.h
.\objects\bsp_board.o: ..\system\include\system.h
.\objects\bsp_board.o: ..\rtthread\include\rtthread.h
.\objects\bsp_board.o: ..\rtthread\bsp\rtconfig.h
.\objects\bsp_board.o: ..\rtthread\include\rtdebug.h
.\objects\bsp_board.o: ..\rtthread\include\rtdef.h
.\objects\bsp_board.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\bsp_board.o: ..\rtthread\include\rtlibc.h
.\objects\bsp_board.o: ..\rtthread\include\libc/libc_stat.h
.\objects\bsp_board.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\bsp_board.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\time.h
.\objects\bsp_board.o: ..\rtthread\include\libc/libc_errno.h
.\objects\bsp_board.o: ..\rtthread\include\libc/libc_fcntl.h
.\objects\bsp_board.o: ..\rtthread\include\libc/libc_ioctl.h
.\objects\bsp_board.o: ..\rtthread\include\libc/libc_dirent.h
.\objects\bsp_board.o: ..\rtthread\include\libc/libc_signal.h
.\objects\bsp_board.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\signal.h
.\objects\bsp_board.o: ..\rtthread\include\libc/libc_fdset.h
.\objects\bsp_board.o: ..\rtthread\include\rtservice.h
.\objects\bsp_board.o: ..\rtthread\include\rtm.h
.\objects\bsp_board.o: ..\rtthread\include\rtthread.h
.\objects\bsp_board.o: ..\system\include\stm32l0xx.h
.\objects\bsp_board.o: ..\system\include\stm32l072xx.h
.\objects\bsp_board.o: ..\system\include\core_cm0plus.h
.\objects\bsp_board.o: ..\system\include\cmsis_version.h
.\objects\bsp_board.o: ..\system\include\cmsis_compiler.h
.\objects\bsp_board.o: ..\system\include\cmsis_armcc.h
.\objects\bsp_board.o: ..\system\include\mpu_armv7.h
.\objects\bsp_board.o: ..\system\include\system_stm32l0xx.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_conf.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rcc.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_def.h
.\objects\bsp_board.o: ..\system\include\stm32l0xx.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\Legacy/stm32_hal_legacy.h
.\objects\bsp_board.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rcc_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_gpio.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_gpio_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_dma.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_cortex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_adc.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_adc_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_comp.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_comp_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_crc.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_crc_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_dac.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_dac_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_flash.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_flash_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_flash_ramfunc.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_i2c.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_i2c_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_i2s.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_iwdg.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_lcd.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_lptim.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_pwr.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_pwr_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rng.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rtc.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rtc_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_spi.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_tim.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_tim_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_tsc.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_uart.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_uart_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_usart.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_usart_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_irda.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_irda_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_smartcard.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_smartcard_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_smbus.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_smbus_ex.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_wwdg.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_pcd.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_ll_usb.h
.\objects\bsp_board.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_pcd_ex.h
.\objects\bsp_board.o: ..\app\include\pin_definitions.h
