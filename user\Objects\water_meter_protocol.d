.\objects\water_meter_protocol.o: ..\protocol\water_meter_protocol.c
.\objects\water_meter_protocol.o: ..\protocol\include\water_meter_protocol.h
.\objects\water_meter_protocol.o: ..\system\include\system.h
.\objects\water_meter_protocol.o: ..\rtthread\include\rtthread.h
.\objects\water_meter_protocol.o: ..\rtthread\bsp\rtconfig.h
.\objects\water_meter_protocol.o: ..\rtthread\include\rtdebug.h
.\objects\water_meter_protocol.o: ..\rtthread\include\rtdef.h
.\objects\water_meter_protocol.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stdarg.h
.\objects\water_meter_protocol.o: ..\rtthread\include\rtlibc.h
.\objects\water_meter_protocol.o: ..\rtthread\include\libc/libc_stat.h
.\objects\water_meter_protocol.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\water_meter_protocol.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\time.h
.\objects\water_meter_protocol.o: ..\rtthread\include\libc/libc_errno.h
.\objects\water_meter_protocol.o: ..\rtthread\include\libc/libc_fcntl.h
.\objects\water_meter_protocol.o: ..\rtthread\include\libc/libc_ioctl.h
.\objects\water_meter_protocol.o: ..\rtthread\include\libc/libc_dirent.h
.\objects\water_meter_protocol.o: ..\rtthread\include\libc/libc_signal.h
.\objects\water_meter_protocol.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\signal.h
.\objects\water_meter_protocol.o: ..\rtthread\include\libc/libc_fdset.h
.\objects\water_meter_protocol.o: ..\rtthread\include\rtservice.h
.\objects\water_meter_protocol.o: ..\rtthread\include\rtm.h
.\objects\water_meter_protocol.o: ..\rtthread\include\rtthread.h
.\objects\water_meter_protocol.o: ..\system\include\stm32l0xx.h
.\objects\water_meter_protocol.o: ..\system\include\stm32l072xx.h
.\objects\water_meter_protocol.o: ..\system\include\core_cm0plus.h
.\objects\water_meter_protocol.o: ..\system\include\cmsis_version.h
.\objects\water_meter_protocol.o: ..\system\include\cmsis_compiler.h
.\objects\water_meter_protocol.o: ..\system\include\cmsis_armcc.h
.\objects\water_meter_protocol.o: ..\system\include\mpu_armv7.h
.\objects\water_meter_protocol.o: ..\system\include\system_stm32l0xx.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_conf.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rcc.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_def.h
.\objects\water_meter_protocol.o: ..\system\include\stm32l0xx.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\Legacy/stm32_hal_legacy.h
.\objects\water_meter_protocol.o: D:\software\Keil\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rcc_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_gpio.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_gpio_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_dma.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_cortex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_adc.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_adc_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_comp.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_comp_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_crc.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_crc_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_dac.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_dac_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_flash.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_flash_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_flash_ramfunc.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_i2c.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_i2c_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_i2s.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_iwdg.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_lcd.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_lptim.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_pwr.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_pwr_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rng.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rtc.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_rtc_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_spi.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_tim.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_tim_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_tsc.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_uart.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_uart_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_usart.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_usart_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_irda.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_irda_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_smartcard.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_smartcard_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_smbus.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_smbus_ex.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_wwdg.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_pcd.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_ll_usb.h
.\objects\water_meter_protocol.o: ..\stm32L0xx_hal\Inc\stm32l0xx_hal_pcd_ex.h
.\objects\water_meter_protocol.o: ..\peripheral\include\uart.h
.\objects\water_meter_protocol.o: ..\app\include\user_config.h
.\objects\water_meter_protocol.o: ..\protocol\include\modbus_rtu.h
